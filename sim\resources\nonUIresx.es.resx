﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="enumFailed" xml:space="preserve">
    <value>Error al enumerar dispositivos</value>
  </data>
  <data name="leftChoose" xml:space="preserve">
    <value>Utilice el botón izquierdo para seleccionar</value>
  </data>
  <data name="noDevice" xml:space="preserve">
    <value>"No hay ningún dispositivo, seleccione"</value>
  </data>
  <data name="notArea" xml:space="preserve">
    <value>?Región no generada!</value>
  </data>
  <data name="openFailed" xml:space="preserve">
    <value>Activar la identificación del dispositivo</value>
  </data>
  <data name="selectIRO" xml:space="preserve">
    <value>Seleccione IRO en el cuadro</value>
  </data>
  <data name="serGainFail" xml:space="preserve">
    <value>?No se pudo establecer la ganancia!</value>
  </data>
  <data name="setFrameFail" xml:space="preserve">
    <value>?No se pudo establecer la velocidad de fotogramas!</value>
  </data>
  <data name="setTimeFail" xml:space="preserve">
    <value>?Error al establecer el tiempo de exposición!</value>
  </data>
  <data name="startGrabFail" xml:space="preserve">
    <value>Error al iniciar la captura</value>
  </data>
  <data name="stopGrabFail" xml:space="preserve">
    <value>Error al finalizar la captura</value>
  </data>
  <data name="sure" xml:space="preserve">
    <value>Por supuesto</value>
  </data>
  <data name="triggerFail" xml:space="preserve">
    <value>Error de desencadenamiento</value>
  </data>
  <data name="cancel" xml:space="preserve">
    <value>Cancelar</value>
  </data>
  <data name="databaseFault" xml:space="preserve">
    <value>La conexión a la base de datos falló</value>
  </data>
  <data name="faultPaw" xml:space="preserve">
    <value>La contrase?a es incorrecta</value>
  </data>
  <data name="loginSuccess" xml:space="preserve">
    <value>Inicio de sesión correcto</value>
  </data>
  <data name="datatype" xml:space="preserve">
    <value>Los tipos de datos no coinciden</value>
  </data>
  <data name="savesuccessfully" xml:space="preserve">
    <value>Conservar con éxito</value>
  </data>
  <data name="fileNotExit" xml:space="preserve">
    <value>El documento no existe！</value>
  </data>
  <data name="notRecord" xml:space="preserve">
    <value>Falta la plantilla</value>
  </data>
  <data name="dbNotOpen" xml:space="preserve">
    <value>La base de datos no está abierta</value>
  </data>
  <data name="saveFault" xml:space="preserve">
    <value>Error al guardar</value>
  </data>
  <data name="saveCen" xml:space="preserve">
    <value>El agujero central se ha guardado correctamente</value>
  </data>
  <data name="noImgOrType" xml:space="preserve">
    <value>Cargar imagen o tipo</value>
  </data>
  <data name="noParamOrHub" xml:space="preserve">
    <value>Falta la fórmula</value>
  </data>
  <data name="boltHoleSave" xml:space="preserve">
    <value>El agujero del perno se ha guardado correctamente</value>
  </data>
  <data name="saveCap" xml:space="preserve">
    <value>El agujero hat se ha guardado correctamente</value>
  </data>
  <data name="paramExit" xml:space="preserve">
    <value>"El parámetro existe, ya sea para reemplazar?"</value>
  </data>
  <data name="lacktable" xml:space="preserve">
    <value>Falta de tablas disponibles</value>
  </data>
  <data name="fileopenClose" xml:space="preserve">
    <value>"El archivo se abre, cierre El archivo y vuelva a cargarlo"</value>
  </data>
  <data name="tableisempty" xml:space="preserve">
    <value>El contenido de la tabla está vacío</value>
  </data>
  <data name="alreadyRe" xml:space="preserve">
    <value>El software está registrado</value>
  </data>
  <data name="modifySucceeded" xml:space="preserve">
    <value>Modificación exitosa</value>
  </data>
  <data name="passwordEmpty" xml:space="preserve">
    <value>La nueva contraseña no puede estar vacía</value>
  </data>
  <data name="passwordInconsistency" xml:space="preserve">
    <value>¡La nueva contraseña introducida dos veces es inconsistente, ¡ por favor, vuelva a ingresar!</value>
  </data>
</root>