﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Sharp7;
namespace CSharp7
{
    public partial class S7Communication : Form
    {
        
        S7Client client;
        //窗口间数据交互委托
     //   public delegate void delegateProcessHImage(HObject hImage);
        //定义数据交互事件
      //  public event delegateProcessHImage eventProcessImage;
        private struct struct_bitdata
        {
            public int bittobytenum, totalnumbit;

        }

        private struct DataAssemble
        {
            public int index, datalength, bitnum;
            public string datatype;
        }


        int result,numsort,recbytenum,sendbytenum;
        byte[] recBuffer ;
        byte[] sendBuffer;
        byte[] sendbytedata=new byte[50];
        short[] sendintdata=new short[50];
        uint[] senduintdata=new uint[50];
        bool[] sendbitdata=new bool[50];
        string[] sendstringdata=new string[50];
        float[] sendfloatdata=new float[50];
        double[] senddoubledata=new double[50];

        byte[] recbytedata = new byte[50];
        short[] recintdata = new short[50];
        uint[] recuintdata = new uint[50];
        bool[] recbitdata = new bool[50];
        string[] recstringdata = new string[50];
        float[] recfloatdata = new float[50];
        double[] recdoubledata = new double[50];


        List<DataAssemble> RecData, SendData;
        public S7Communication()
        {
            InitializeComponent();
            


           client = new S7Client();
           //result = client.ConnectTo("*************", 0, 1);
           //if (result == 0)
           //{
           //    Console.WriteLine("Connected to *************");
           //}
           //else
           //{
           //    Console.WriteLine(client.ErrorText(result));
           //    //Console.ReadKey();
           //    return;
           //}
          // button3.Enabled = false;
        //   button4.Enabled = false;
            RecData=new List<DataAssemble>();
            SendData = new List<DataAssemble>();
            S7Send.Enabled = false;
            S7Recieve.Enabled = false;
            button3.Enabled = false;
            button4.Enabled = false;
            cbB_datatype.Enabled = false;
            NUD_Numbyte.Enabled = false;
            tB_Status.BackColor = Color.Red;
            listView1.AutoResizeColumns(ColumnHeaderAutoResizeStyle.HeaderSize);
        }

        private void S7Send_Click(object sender, EventArgs e)
        {
 
 /*******************************
 * ****************************
 * 发送数据*****************/
          //  foreach (ListViewItem item in listView1.Items)

            ReadListview();
            if (SendData.Count != 0)
            {
                int start_index = 0;

               int bitnum=-1,bytenum=-1,intnum=-1,unint=-1,floatnum=-1,doublenum=-1,stringnum=-1;

                for (int i = 0; i < SendData.Count;i++ )
                  
                    {

                        string subitem2;
                        int subitem1 = 0;

                       // subitem1 = SendData[i].datalength;
                        subitem2 = SendData[i].datatype;

                        //       int listviewindex = int.Parse(listView1.Items[i].Text);

                        if (SendData[i].index == 0)
                            start_index = 0;
                        if (SendData[i].index > 0)
                        {
                            if (SendData[i-1].datatype == "string")
                                start_index += 256;
                            else
                            {
                                subitem1 = SendData[i-1].datalength;
                                start_index += subitem1;
                            }

                        }
                    



                        switch (subitem2)
                        {
                            case "bit":
                              
                                    int floor = SendData[i].bitnum / 8;
                                    int leftbir = SendData[i].bitnum % 8;
                                    int floornext = start_index;

                                    for (int j = 0; j < floor; j++)
                                    {

                                        for (int n = 0; n < 8; n++)
                                        {
                                            bitnum++;
                                            sendbitdata[bitnum] = true;
                                            S7.SetBitAt(ref sendBuffer, (start_index + j), n, sendbitdata[bitnum]);

                                            MessageBox.Show(bitnum.ToString() + sendbitdata[bitnum].ToString());
                                        }
                                        floornext = start_index + j + 1;

                                    }
                                    for (int m = 0; m < leftbir; m++)
                                    {
                                        bitnum++;
                                        sendbitdata[bitnum] = true;
                                        S7.SetBitAt(ref sendBuffer, floornext, m, sendbitdata[bitnum]);

                                        MessageBox.Show(bitnum.ToString() + sendbitdata[bitnum].ToString());

                                    }

                                
                                break;
                            case "byte":
                                bytenum++;
                                sendbytedata[bytenum] = 4;
                                S7.SetByteAt(sendBuffer, start_index, sendbytedata[bytenum]);
                                break;
                            case "int":
                                intnum++;
                                sendintdata[intnum] = 108;
                                S7.SetIntAt(sendBuffer, start_index, sendintdata[intnum]);
                                MessageBox.Show(sendintdata[intnum].ToString());
                                break;
                            case "uint":
                                ;
                                break;
                            case "float":
                                floatnum++;
                                sendfloatdata[floatnum] = (float)13.55;
                                S7.SetRealAt(sendBuffer, start_index, (float)sendfloatdata[floatnum]);
                                MessageBox.Show(sendfloatdata[floatnum].ToString());
                                break;
                            case "double":
                                doublenum++;
                                senddoubledata[doublenum] = 34.5;
                                S7.SetLRealAt(sendBuffer, start_index, senddoubledata[doublenum]);
                                break;
                            case "string":
                                stringnum++;
                                sendstringdata[stringnum] = "errrr";
                                S7.SetStringAt(sendBuffer, start_index, 254, sendstringdata[stringnum]);
                                MessageBox.Show(sendstringdata[stringnum]);
                                break;
                        }
                    }        



            }
             else
                 MessageBox.Show("发送数据区为空，请添加数据！");







        //    S7.SetBitAt(ref sendBuffer, 0, 0, true);
       //     S7.SetByteAt(sendBuffer, 0, 1);
        //    S7.SetIntAt(sendBuffer, 0, 10);
       //     S7.SetRealAt(sendBuffer, 2, (float)4.36);
            int senddb=int.Parse(tB_SendDB.Text.ToString());
            result = client.DBWrite(3, 0, sendBuffer.Length, sendBuffer);//第二个参数为PLCDATABSEE中的起始位置
        }
      


        private void S7Recieve_Click(object sender, EventArgs e)
        {
            
            ReadListview();
            int recdb = int.Parse(tB_RecDB.Text.ToString());
            result = client.DBRead(recdb, 0, recBuffer.Length, recBuffer);
            if (RecData.Count != 0)
            {
                int start_index = 0;

                int bitnum = -1, bytenum = -1, intnum = -1, unint = -1, floatnum = -1, doublenum = -1, stringnum = -1;

                for (int i = 0; i < RecData.Count; i++)
                {

                    string subitem2;
                    int subitem1 = 0;

                    // subitem1 = SendData[i].datalength;
                    subitem2 = RecData[i].datatype;

                    //       int listviewindex = int.Parse(listView1.Items[i].Text);

                    if (RecData[i].index == 0)
                        start_index = 0;
                    if (RecData[i].index > 0)
                    {
                        if (RecData[i - 1].datatype == "string")
                            start_index += 256;
                        else
                        {
                            subitem1 = RecData[i - 1].datalength;
                            start_index += subitem1;
                        }

                    }




                    switch (subitem2)
                    {
                        case "bit":
  
                                int floor = RecData[i].bitnum / 8;
                                int leftbir = RecData[i].bitnum % 8;
                                int floornext = start_index;

                                for (int j = 0; j < floor; j++)
                                {
                                    
                                    for (int n = 0; n < 8; n++)
                                    {
                                        bitnum++;
                                        recbitdata[bitnum] = S7.GetBitAt(recBuffer, (start_index + j), n);
                                        
                                        MessageBox.Show(bitnum.ToString()+recbitdata[bitnum].ToString());
                                    }
                                    floornext = start_index + j + 1;

                                }
                                for (int m = 0; m < leftbir; m++)
                                {
                                    bitnum++;
                                    recbitdata[bitnum] = S7.GetBitAt(recBuffer, floornext, m);
                                    MessageBox.Show(bitnum.ToString()+recbitdata[bitnum].ToString());

                                }

                                break;
                        case "byte":
                            bytenum++;
                           
                            recbytedata[bytenum] = S7.GetByteAt(recBuffer, start_index);
                            MessageBox.Show(recbytedata[bytenum].ToString());
                            break;
                        case "int":
                            intnum++;
                             
                             recintdata[intnum] =(short) S7.GetIntAt(recBuffer, start_index);
                             MessageBox.Show(recintdata[intnum].ToString());
                            break;
                        case "uint":
                            ;
                            break;
                        case "float":
                            floatnum++;

                            recfloatdata[floatnum] = S7.GetRealAt(recBuffer, start_index);
                            MessageBox.Show(recfloatdata[floatnum].ToString());
                            break;
                        case "double":
                            doublenum++;

                            recdoubledata[doublenum] = S7.GetLRealAt(recBuffer, start_index);
                            MessageBox.Show(recdoubledata[doublenum].ToString());
                            break;
                        case "string":
                            stringnum++;
                            recstringdata[stringnum] = S7.GetStringAt(recBuffer, start_index);
                            MessageBox.Show(recstringdata[stringnum]);
                            break;
                    }
                }



            }
            else
                MessageBox.Show("接收数据区为空，请添加数据！");

           










         
            if (result != 0)
            {
                Console.WriteLine("Error: " + client.ErrorText(result));
            }
           
          

          
          
        }



        /* 定时器的运用 */
        int num = 0;
        private void timer1_Tick(object sender, EventArgs e)
        {

            timer1.Interval = 1000;
            num++;
            MessageBox.Show(num.ToString());
        }

        private void button2_Click(object sender, EventArgs e)
        {
            timer1.Stop();
        }

        private void button1_Click(object sender, EventArgs e)
        {
            timer1.Start();
        }

        private void button3_Click(object sender, EventArgs e)
        {
          //  byte[] recBuffer = new byte[recbytenum];
         //   byte[] sendBuffer = new byte[sendbytenum];


            if (cbB_datatype.Text != "")
            {
                ListViewItem item = new ListViewItem();
                //   item.SubItems.Clear();
                /**名称**/
                numsort++;
                item.Text = numsort.ToString();
                item.SubItems.Add(NUD_Numbyte.Value.ToString());

                item.SubItems.Add(cbB_datatype.Text);
                //    item.SubItems.Add("02");

                /**人数**/
                //  item.SubItems.Add("03");
                //   item.SubItems.Add("04");

                this.listView1.Items.Add(item);
            }
            else
                return;

          


            }

        private void button4_Click(object sender, EventArgs e)
        {
           

                foreach (ListViewItem item in listView1.Items)
                {
                    if (listView1.SelectedItems.Contains(item))
                    {
                        int indexDel = listView1.Items.IndexOf(listView1.FocusedItem);
                        if (listView1.SelectedItems.Count != 0)
                        {
                            listView1.Items.RemoveAt(indexDel);//删除
                        }
                    }

                }
                //foreach (ListViewItem item in listView1.Items)
                //{
                //    string getSubstr = item.SubItems[1].Text;
                //    totalnum += int.Parse(getSubstr);
                //}

                //if (radB_rec.Checked == true)
                //{

                //    recbytenum = totalnum;
                //    recBuffer = new byte[recbytenum];
                //}

                //if (radB_send.Checked == true)
                //{

                //    sendbytenum = totalnum;
                //    sendBuffer = new byte[sendbytenum];
                //}
            
           

            
            

        }

        private void SelectedIndexChanged(object sender, EventArgs e)
        {

            /*
          0 Byte    8 Bit Word                     (All)
          1 Word   16 Bit Word                     (All)
          2 DWord  32 Bit Word                     (All)
          3 LWord  64 Bit Word                     (S71500)
          4 USint   8 Bit Unsigned Integer         (S71200/1500)
          5 UInt   16 Bit Unsigned Integer         (S71200/1500)
          6 UDInt  32 Bit Unsigned Integer         (S71200/1500)
          7 ULint  64 Bit Unsigned Integer         (S71500)
          8 Sint    8 Bit Signed Integer           (S71200/1500)
          9 Int    16 Bit Signed Integer           (All)
         10 DInt   32 Bit Signed Integer           (S71200/1500)
         11 LInt   64 Bit Signed Integer           (S71500)
         12 Real   32 Bit Floating point           (All)
         13 LReal  64 Bit Floating point           (S71200/1500)
         14 Time   32 Bit Time elapsed ms          (All)
         15 LTime  64 Bit Time Elapsed ns          (S71500)
         16 Date   16 Bit days from 1990/1/1       (All)
         17 TOD    32 Bit ms elapsed from midnight (All)
         18 DT      8 Byte Date and Time           (All)
         19 LTOD   64 Bit time of day (ns)         (S71500)
         20 DTL    12 Byte Date and Time Long      (S71200/1500)
         21 LDT    64 Bit ns elapsed from 1970/1/1 (S71500)
*/
          string selectedIndextext=  cbB_datatype.Text;
          if (selectedIndextext == "bit" || selectedIndextext == "byte" || selectedIndextext == "int" || selectedIndextext == "uint" || selectedIndextext == "float" || selectedIndextext == "double" || selectedIndextext == "char")
          {
              NUD_Numbyte.Enabled = false;
              switch (selectedIndextext)
              {
                 
                  case "byte":
                  case "uint":
                      NUD_Numbyte.Value=2;
                      break;
                  case "float":
                     NUD_Numbyte.Value = 4;
                      break;
                  case "int":
                      NUD_Numbyte.Value=2;
                      break;
                  case "double":
                      NUD_Numbyte.Value = 8;
                      break;
                  case "bit":
                  case "char":
                      NUD_Numbyte.Value = 1;
                      break;
              }

          }
          else
              NUD_Numbyte.Enabled = true;


        }

        private void radB_rec_CheckedChanged(object sender, EventArgs e)
        {
            S7Send.Enabled = false;
            S7Recieve.Enabled = true;
            labdataregion.Text = "接收数据块";
        }

        private void radB_send_CheckedChanged(object sender, EventArgs e)
        {
            S7Send.Enabled = true;
            S7Recieve.Enabled = false;
            labdataregion.Text = "发送数据块";
        }

        private void ConnectBtn_Click(object sender, EventArgs e)
        {
            




            int Rack = System.Convert.ToInt32(TxtRack.Text);
            int Slot = System.Convert.ToInt32(TxtSlot.Text);
        //    result = client.ConnectTo("*************", 0, 1);
            result = client.ConnectTo(TxtIP.Text, Rack, Slot);
            if (result == 0)
            {
              button3.Enabled = true;
              button4.Enabled = true;
            }
            else
            {
                Console.WriteLine(client.ErrorText(result));
                //Console.ReadKey();
                return;
            }


            if (result == 0)
            {
                TextError.Text = "Connected to *************" + " PDU Negotiated : " + client.PduSizeNegotiated.ToString();
                TxtIP.Enabled = false;
                TxtRack.Enabled = false;
                TxtSlot.Enabled = false;
                ConnectBtn.Enabled = false;
                DisconnectBtn.Enabled = true;
                S7Send.Enabled = true;
                S7Recieve.Enabled = true;
                cbB_datatype.Enabled = true;
                NUD_Numbyte.Enabled = true;
                tB_Status.BackColor = Color.Green;
                if (radB_rec.Checked == true)
                {
                    S7Send.Enabled = false;
                    S7Recieve.Enabled = true;
                }
                if (radB_send.Checked == true)
                {
                    S7Send.Enabled = true;
                    S7Recieve.Enabled = false;
                }



               
            }
        }

  private void ShowResult(int Result)
        {
            // This function returns a textual explaination of the error code
            TextError.Text = client.ErrorText(Result);
            if (Result == 0)
                TextError.Text = TextError.Text + " (" + client.ExecutionTime.ToString() + " ms)";
        }



  private void DisconnectBtn_Click(object sender, EventArgs e)
  {
      client.Disconnect();
      TextError.Text = "Disconnected";
      TxtIP.Enabled = true;
      TxtRack.Enabled = true;
      TxtSlot.Enabled = true;
      ConnectBtn.Enabled = true;
      DisconnectBtn.Enabled = false;
      button3.Enabled = false;
      button4.Enabled = false;
      S7Send.Enabled = false;
      S7Recieve.Enabled = false;
      cbB_datatype.Enabled = false;
      NUD_Numbyte.Enabled = false;
      tB_Status.BackColor = Color.Red;
  }

     

  
private void ReadListview()
{
    int totalnum = 0;
    struct_bitdata[] subaddbyte = new struct_bitdata[10];

    struct_bitdata temple;
    temple.bittobytenum = 0;
    temple.totalnumbit = 0;
    int indexsubaddbyte = -1, indexdataAssem = -1;
    List<struct_bitdata> addbyte = new List<struct_bitdata>();//每连续8个bit新建一个BYTE
    List<DataAssemble> resort = new List<DataAssemble>();//重新排序
    for (int i = 0; i < listView1.Items.Count; i++)
    {

        string subitem2;
        subitem2 = listView1.Items[i].SubItems[2].Text;
        DataAssemble dataAssem;
        dataAssem.bitnum = 0;
        dataAssem.datalength = 0;
        dataAssem.datatype = "";
        dataAssem.index = 0;
        switch (subitem2)
        {
            case "bit":


                if ((listView1.Items[i].Index == 0) || (listView1.Items[i].SubItems[2].Text) != (listView1.Items[i - 1].SubItems[2].Text))
                {
                    temple.bittobytenum = 0;
                    temple.totalnumbit = 0;
                    indexsubaddbyte++;
                    subaddbyte[indexsubaddbyte].bittobytenum += 1;
                    subaddbyte[indexsubaddbyte].totalnumbit += 1;
                    addbyte.Add(subaddbyte[indexsubaddbyte]);

                    temple = subaddbyte[indexsubaddbyte];


                }
                else
                {
                    temple.totalnumbit += 1;
                    //判断字节的数目
                    if (i < (listView1.Items.Count - 1))
                    {
                        if ((listView1.Items[i].SubItems[2].Text) != (listView1.Items[i + 1].SubItems[2].Text))
                        {
                            if (temple.totalnumbit > 8)
                            {
                                double k = ((float)temple.totalnumbit - 8) / 8;
                                temple.bittobytenum += (int)Math.Ceiling(k);
                               
                            }
                            double k1 = (float)temple.bittobytenum / 2;
                            temple.bittobytenum = ((int)Math.Ceiling(k1)) * 2;

                        }
                    }
                    else
                    {
                        if (temple.totalnumbit > 8)
                        {
                            double k = ((float)temple.totalnumbit - 8) / 8;
                            temple.bittobytenum += (int)Math.Ceiling(k);
                           
                        }
                        double k1 = (float)temple.bittobytenum / 2;
                        temple.bittobytenum = ((int)Math.Ceiling(k1)) * 2;

                    }



                    if (i < (listView1.Items.Count - 1))
                    {
                        if ((listView1.Items[i].SubItems[2].Text) != (listView1.Items[i + 1].SubItems[2].Text))
                        {
                            totalnum += temple.bittobytenum;
                            /*************************************
                       * ***********************************
                       * 重新排序*************************/
                            indexdataAssem++;

                            dataAssem.index = indexdataAssem;
                            dataAssem.datatype = "bit";
                            dataAssem.datalength = temple.bittobytenum;
                            dataAssem.bitnum = temple.totalnumbit;
                            resort.Add(dataAssem);
                        }

                    }
                    else
                    {
                        totalnum += temple.bittobytenum;
                        /*************************************
                         * ***********************************
                         * 重新排序*************************/
                        indexdataAssem++;

                        dataAssem.index = indexdataAssem;
                        dataAssem.datatype = "bit";
                        dataAssem.datalength = temple.bittobytenum;
                        dataAssem.bitnum = temple.totalnumbit;
                        resort.Add(dataAssem);
                    }


                }
                break;
            case "byte":
                totalnum += 2;

                indexdataAssem++;
                dataAssem.index = indexdataAssem;
                dataAssem.datatype = "byte";
                dataAssem.datalength = 2;
                resort.Add(dataAssem);
                break;
            case "int":
                totalnum += 2;

                indexdataAssem++;
                dataAssem.index = indexdataAssem;
                dataAssem.datatype = "int";
                dataAssem.datalength = 2;
                resort.Add(dataAssem);
                break;
            case "string":
                totalnum += 256;

                indexdataAssem++;
                dataAssem.index = indexdataAssem;
                dataAssem.datatype = "string";
                dataAssem.datalength = 256;
                resort.Add(dataAssem);

                break;
            case "float":
                totalnum += 4;

                indexdataAssem++;
                dataAssem.index = indexdataAssem;
                dataAssem.datatype = "float";
                dataAssem.datalength = 4;
                resort.Add(dataAssem);
                break;


        }


    }
    foreach (DataAssemble item in resort)
    {

        MessageBox.Show(item.index.ToString() + ":" + item.datatype + ":" + item.datalength.ToString() + ":" + item.bitnum.ToString());

    }


    MessageBox.Show(totalnum.ToString());
    if (radB_rec.Checked == true)
    {
        RecData = resort;
        recbytenum = totalnum;
        recBuffer = new byte[recbytenum];
    }

    if (radB_send.Checked == true)
    {
        SendData = resort;
        sendbytenum = totalnum;
        sendBuffer = new byte[sendbytenum];
    }
}






   
    }
}
