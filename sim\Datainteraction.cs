﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace BingSolhalcon
{
   public class Datainteraction
    {
      public  byte[] sendbytedata = new byte[50];
      public short[] sendintdata = new short[50];
      public uint[] senduintdata = new uint[50];
      public bool[] sendbitdata = new bool[50];
      public string[] sendstringdata = new string[50];
      public float[] sendfloatdata = new float[50];
      public double[] senddoubledata = new double[50];

      public byte[] recbytedata = new byte[50];
      public short[] recintdata = new short[50];
      public uint[] recuintdata = new uint[50];
      public bool[] recbitdata = new bool[50];
      public string[] recstringdata = new string[50];
      public float[] recfloatdata = new float[50];
      public  double[] recdoubledata = new double[50];
    }
}
