﻿using System;
using System.IO;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using HalconDotNet;
using System.Threading;
using System.Collections.Concurrent;
using BingSolhalcon.UI;

using DevExpress.XtraBars.Ribbon;
using DevExpress.LookAndFeel;
using BingSolhalcon.resources;

namespace BingSolhalcon
{



    public partial class Form1 : RibbonForm
    {
        //窗口缩放用变量
        private float x;//定义当前窗体的宽度
        private float y;//定义当前窗体的高度
                        
        
        private Datainteraction sendDatainter = new Datainteraction();
        private Datainteraction receiveDatainter = new Datainteraction();
        
        private string[] m_typenamerecord = { "nowrecord", "lastrecord" };//前、后两轮毂的记录
        private string constr;//连接数据库用字句
        private bool[] m_c1btnstatus, m_c2btnstatus, m_c3btnstatus, m_c4btnstatus;
        
        

        ComponentResourceManager res = new ComponentResourceManager(typeof(nonUIresx)); //自定义资源字段

        


        public Form1()
        {
            InitializeComponent();


            //窗口缩放用
            x = this.Width;
            y = this.Height;
            setTag(this);

            //窗口缩放用




            m_c1btnstatus = new bool[4] { true, false, true, false };
            m_c2btnstatus = new bool[4] { true, false, true, false };
            m_c3btnstatus = new bool[4] { true, false, true, false };
            m_c4btnstatus = new bool[4] { true, false, true, false };
            Control.CheckForIllegalCrossThreadCalls = false;

            loadConfig();

            constr = @"Server=" + Edit_Databasename.EditValue + ";Database=MyDB;Trusted_Connection=SSPI";
        }

        private void loadConfig()
        {
            try
            {
                // 读配置文件
                if (File.Exists(Application.StartupPath + "\\config.ini"))
                {
                    Edit_Databasename.EditValue = ConfigIni.GetIniKeyValue("通讯地址", "数据库名称", ".", Application.StartupPath + "\\config.ini");
                }
            }
            catch (Exception e)
            {
                MessageBox.Show("配置加载失败\n" + e.Message);
            }
        }

        private void setTag(Control cons)
        {
            foreach (Control con in cons.Controls)
            {
                con.Tag = con.Width + ";" + con.Height + ";" + con.Left + ";" + con.Top + ";" + con.Font.Size;
                if (con.Controls.Count > 0)
                {
                    setTag(con);
                }
            }
        }

        private void setControls(float newx, float newy, Control cons)
        {
            //遍历窗体中的控件，重新设置控件的值
            foreach (Control con in cons.Controls)
            {
                //获取控件的Tag属性值，并分割后存储字符串数组
                if (con.Tag != null)
                {
                    string[] mytag = con.Tag.ToString().Split(new char[] { ';' });
                    //根据窗体缩放的比例确定控件的值
                    con.Width = Convert.ToInt32(System.Convert.ToSingle(mytag[0]) * newx);//宽度
                    con.Height = Convert.ToInt32(System.Convert.ToSingle(mytag[1]) * newy);//高度
                    con.Left = Convert.ToInt32(System.Convert.ToSingle(mytag[2]) * newx);//左边距
                    con.Top = Convert.ToInt32(System.Convert.ToSingle(mytag[3]) * newy);//顶边距
                    Single currentSize = System.Convert.ToSingle(mytag[4]) * newy;//字体大小
                    con.Font = new Font(con.Font.Name, currentSize, con.Font.Style, con.Font.Unit);
                    if (con.Controls.Count > 0)
                    {
                        setControls(newx, newy, con);
                    }
                }
            }
        }

        

        private void Form1_Load(object sender, EventArgs e)
        {

            Directory.SetCurrentDirectory(Application.StartupPath);

            if (!Directory.Exists(Application.StartupPath + "\\model"))
            {
                Directory.CreateDirectory(Application.StartupPath + "\\model");
            }
            if (!Directory.Exists(Application.StartupPath + "\\model_vir\\lasershm"))
            {
                Directory.CreateDirectory(Application.StartupPath + "\\model_vir\\lasershm");
            }
            if (!Directory.Exists(Application.StartupPath + "\\model_vir\\Measurement"))
            {
                Directory.CreateDirectory(Application.StartupPath + "\\model_vir\\Measurement");
            }
            if (!Directory.Exists(Application.StartupPath + "\\model_vir\\mtr"))
            {
                Directory.CreateDirectory(Application.StartupPath + "\\model_vir\\mtr");
            }
            if (!Directory.Exists(Application.StartupPath + "\\model_vir\\registerpicture"))
            {
                Directory.CreateDirectory(Application.StartupPath + "\\model_vir\\registerpicture");
            }




            //halcon窗口分别初始化
            this.Height = 900;

            hWindowControlC1.Left = 0;
            //hWindowControlC1.Top = 0;

            this.label_Camera1status.ItemAppearance.Normal.BackColor = Color.Red;
            this.label_Camera2status.ItemAppearance.Normal.BackColor = Color.Red;
            this.label_Camera3status.ItemAppearance.Normal.BackColor = Color.Red;
            this.label_Camera4status.ItemAppearance.Normal.BackColor = Color.Red;
            this.label_DBStatus.ItemAppearance.Normal.BackColor = Color.Red;
            this.Btm_DisConnect.Enabled = false;



            UserLookAndFeel.Default.SetSkinStyle("Office 2010 Blue");


        }


        private void Form1_FormClosing(object sender, FormClosingEventArgs e)
        {
        }



        private void Form1_FormClosed(object sender, FormClosedEventArgs e)
        {

        }



        private void Disp_systemtiem(object sender, EventArgs e)
        {

            label_Systermtime.Caption = DateTime.Now.ToString();


        }


        private void barButtonItem9_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {

        }


        private void ribbonControl1_Click(object sender, EventArgs e)
        {

        }


        

        private void LoadAll(Form form)
        {
            MultiLanguage multiLanguage = new MultiLanguage();
            multiLanguage.LoadLanguage(form, typeof(Form1));
            if (form.Name == "Form1")
            {
                multiLanguage.LoadLanguage(form, typeof(Form1));
            }
            if (form.Name == "Patterm")
            {
                multiLanguage.LoadLanguage(form, typeof(Patterm));
            }

        }




        private void zh_barStaticItem_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            MultiLanguage multiLanguage = new MultiLanguage();
            //修改默认语言
            multiLanguage.SetDefaultLanguage("zh");
            //对所有打开的窗口重新加载语言
            FormCollection collection = Application.OpenForms;
            for (int i = 0; i < collection.Count; i++)
            {
                LoadAll(collection[i]);
            }
        }

        private void barStaticItem4_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Type FormType = this.GetType();

            MultiLanguage multiLanguage = new MultiLanguage();
            //修改默认语言
            multiLanguage.SetDefaultLanguage("en");
            //对所有打开的窗口重新加载语言
            FormCollection collection = Application.OpenForms;
            for (int i = 0; i < collection.Count; i++)
            {
                LoadAll(collection[i]);
            }

        }

        private void es_barStaticItem_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            MultiLanguage multiLanguage = new MultiLanguage();
            //修改默认语言
            multiLanguage.SetDefaultLanguage("es");
            //对所有打开的窗口重新加载语言
            FormCollection collection = Application.OpenForms;
            for (int i = 0; i < collection.Count; i++)
            {
                LoadAll(collection[i]);
            }
        }

        private void barButtonItem3_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Type FormType = this.GetType();

            MultiLanguage multiLanguage = new MultiLanguage();
            //修改默认语言
            multiLanguage.SetDefaultLanguage("fr");
            //对所有打开的窗口重新加载语言
            FormCollection collection = Application.OpenForms;
            for (int i = 0; i < collection.Count; i++)
            {
                LoadAll(collection[i]);
            }
        }




        private void Getdatabasename(ref string databasename, ref string numberofdecima)
        {
            databasename = Edit_Databasename.EditValue.ToString();

        }


        
        

        private void Btm_FindTemplate_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Patterm m_pattem = new Patterm();
            m_pattem.evenGetDatabasename += Getdatabasename;
            m_pattem.Show();
        }

        private void btn_circlecaliper_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            CircleCaliper m_circlecaliper = new CircleCaliper();
            m_circlecaliper.evenGetDatabasename += Getdatabasename;
            m_circlecaliper.Show();

        }
    }
}
