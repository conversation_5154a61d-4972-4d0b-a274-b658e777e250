﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="enumFailed" xml:space="preserve">
    <value>échec de l' énumération du périphérique</value>
  </data>
  <data name="leftChoose" xml:space="preserve">
    <value>S'il vous pla?t utiliser le bouton gauche de la souris pour sélectionner</value>
  </data>
  <data name="noDevice" xml:space="preserve">
    <value>"Aucun périphérique, Veuillez sélectionner"</value>
  </data>
  <data name="notArea" xml:space="preserve">
    <value>Zone non générée!</value>
  </data>
  <data name="openFailed" xml:space="preserve">
    <value>Activer la reconnaissance du périphérique</value>
  </data>
  <data name="selectIRO" xml:space="preserve">
    <value>Veuillez cocher la caseIRO</value>
  </data>
  <data name="serGainFail" xml:space="preserve">
    <value>Impossible de définir le gain!</value>
  </data>
  <data name="setFrameFail" xml:space="preserve">
    <value>Impossible de définir le taux d'images!</value>
  </data>
  <data name="setTimeFail" xml:space="preserve">
    <value>Impossible de définir le temps d ’ exposition!</value>
  </data>
  <data name="startGrabFail" xml:space="preserve">
    <value>échec du lancement de la capture</value>
  </data>
  <data name="stopGrabFail" xml:space="preserve">
    <value>échec de la capture de fin</value>
  </data>
  <data name="sure" xml:space="preserve">
    <value>Ok</value>
  </data>
  <data name="triggerFail" xml:space="preserve">
    <value>Echec du déclenchement</value>
  </data>
  <data name="cancel" xml:space="preserve">
    <value>Annuler</value>
  </data>
  <data name="databaseFault" xml:space="preserve">
    <value>La connexion à la base de données a échoué</value>
  </data>
  <data name="faultPaw" xml:space="preserve">
    <value>Mot de passe incorrect</value>
  </data>
  <data name="loginSuccess" xml:space="preserve">
    <value>Connexion réussie</value>
  </data>
  <data name="datatype" xml:space="preserve">
    <value>Type de données non concordant</value>
  </data>
  <data name="savesuccessfully" xml:space="preserve">
    <value>Enregistrement réussi</value>
  </data>
  <data name="fileNotExit" xml:space="preserve">
    <value>Le fichier n'existe pas！</value>
  </data>
  <data name="notRecord" xml:space="preserve">
    <value>Modèle manquant</value>
  </data>
  <data name="dbNotOpen" xml:space="preserve">
    <value>La base de données n'est pas ouverte</value>
  </data>
  <data name="saveFault" xml:space="preserve">
    <value>échec de l'enregistrement</value>
  </data>
  <data name="saveCen" xml:space="preserve">
    <value>Le trou central a été enregistré avec succès</value>
  </data>
  <data name="noImgOrType" xml:space="preserve">
    <value>Chargement d'une image ou d'un type</value>
  </data>
  <data name="noParamOrHub" xml:space="preserve">
    <value>Formule manquante</value>
  </data>
  <data name="boltHoleSave" xml:space="preserve">
    <value>Le trou de boulon a été enregistré avec succès</value>
  </data>
  <data name="saveCap" xml:space="preserve">
    <value>L'arrêt du chapeau a été enregistré avec succès</value>
  </data>
  <data name="paramExit" xml:space="preserve">
    <value>"Le paramètre existe, s'il est remplacé?"</value>
  </data>
  <data name="lacktable" xml:space="preserve">
    <value>Tables disponibles manquantes</value>
  </data>
  <data name="fileopenClose" xml:space="preserve">
    <value>"Le fichier est ouvert, fermez-le, rechargez-le"</value>
  </data>
  <data name="tableisempty" xml:space="preserve">
    <value>Le contenu du tableau est vide</value>
  </data>
  <data name="alreadyRe" xml:space="preserve">
    <value>Le logiciel est déjà enregistré</value>
  </data>
  <data name="modifySucceeded" xml:space="preserve">
    <value>Modification réussie</value>
  </data>
  <data name="passwordEmpty" xml:space="preserve">
    <value>Le nouveau mot de passe ne peut pas être vide</value>
  </data>
  <data name="passwordInconsistency" xml:space="preserve">
    <value>Le nouveau mot de passe entré deux fois n'est pas cohérent, veuillez le saisir à nouveau!</value>
  </data>
</root>