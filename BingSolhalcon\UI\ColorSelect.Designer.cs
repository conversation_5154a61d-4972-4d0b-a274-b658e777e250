﻿
namespace BingSolhalcon.UI
{
    partial class ColorSelect
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ColorSelect));
            this.RibbonControl1 = new DevExpress.XtraBars.Ribbon.RibbonControl();
            this.Btn_ColorSelect = new DevExpress.XtraEditors.SimpleButton();
            this.cbBox_Color = new DevExpress.XtraEditors.ComboBoxEdit();
            ((System.ComponentModel.ISupportInitialize)(this.RibbonControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbBox_Color.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // RibbonControl1
            // 
            this.RibbonControl1.ExpandCollapseItem.Id = 0;
            this.RibbonControl1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.RibbonControl1.ExpandCollapseItem});
            resources.ApplyResources(this.RibbonControl1, "RibbonControl1");
            this.RibbonControl1.MaxItemId = 1;
            this.RibbonControl1.Name = "RibbonControl1";
            // 
            // Btn_ColorSelect
            // 
            this.Btn_ColorSelect.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("Btn_ColorSelect.Appearance.Font")));
            this.Btn_ColorSelect.Appearance.Options.UseFont = true;
            resources.ApplyResources(this.Btn_ColorSelect, "Btn_ColorSelect");
            this.Btn_ColorSelect.Name = "Btn_ColorSelect";
            this.Btn_ColorSelect.Click += new System.EventHandler(this.Btn_ColorSelect_Click);
            // 
            // cbBox_Color
            // 
            resources.ApplyResources(this.cbBox_Color, "cbBox_Color");
            this.cbBox_Color.MenuManager = this.RibbonControl1;
            this.cbBox_Color.Name = "cbBox_Color";
            this.cbBox_Color.Properties.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("cbBox_Color.Properties.Appearance.Font")));
            this.cbBox_Color.Properties.Appearance.Options.UseFont = true;
            this.cbBox_Color.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("cbBox_Color.Properties.Buttons"))))});
            this.cbBox_Color.Properties.Items.AddRange(new object[] {
            resources.GetString("cbBox_Color.Properties.Items"),
            resources.GetString("cbBox_Color.Properties.Items1"),
            resources.GetString("cbBox_Color.Properties.Items2")});
            // 
            // ColorSelect
            // 
            this.AcceptButton = this.Btn_ColorSelect;
            this.AllowDisplayRibbon = false;
            this.Appearance.Options.UseFont = true;
            this.AutoHideRibbon = false;
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.cbBox_Color);
            this.Controls.Add(this.Btn_ColorSelect);
            this.Controls.Add(this.RibbonControl1);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedToolWindow;
            this.Name = "ColorSelect";
            this.Ribbon = this.RibbonControl1;
            this.RibbonVisibility = DevExpress.XtraBars.Ribbon.RibbonVisibility.Hidden;
            this.Load += new System.EventHandler(this.ColorSelect_Load);
            ((System.ComponentModel.ISupportInitialize)(this.RibbonControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbBox_Color.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraBars.Ribbon.RibbonControl RibbonControl1;
        private DevExpress.XtraEditors.SimpleButton Btn_ColorSelect;
        private DevExpress.XtraEditors.ComboBoxEdit cbBox_Color;
    }
}