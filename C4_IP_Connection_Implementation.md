# C4 3D相机IP地址连接功能实现

## 概述
本次修改为BingSolhalcon项目中的C4 3D相机实现了通过IP地址连接的功能，使其与C1、C2、C3相机保持一致的连接方式。

## 修改内容

### 1. HKVision_3D.cs 修改

#### 新增成员变量
- 添加了 `m_nCurrentDeviceIndex` 用于保存当前连接设备的索引

#### 新增方法
- `DeviceListAcqByIP()` - 通过IP地址过滤枚举3D相机
- `ConvertUInt32ToIP()` - 将UInt32格式的IP地址转换为字符串格式
- `Open_Camera_ByIP()` - 通过IP地址连接3D相机
- `ConnectToDevice()` - 连接到指定设备的私有方法
- `StartGrab()` - 无参数版本的开始采集方法

#### 修改方法
- `Open_Camera()` - 添加了设备索引保存和错误检查

### 2. Form1.cs 修改

#### 配置文件读取
- 添加了C4相机IP地址的配置读取：
  ```csharp
  string camera4IP = ConfigIni.GetIniKeyValue("CameraIP", "C4", "*************", configPath);
  ```

#### 连接逻辑修改
- 初始化连接：将 `m_HKVision_3D1.Open_Camera("C4", m_DeviceList_3D)` 改为 `m_HKVision_3D1.Open_Camera_ByIP("C4", camera4IP)`
- 重新连接：同样修改为使用IP地址连接
- StartGrab调用：移除了索引参数，使用无参数版本

### 3. 配置文件
创建了示例配置文件 `camera_config_example.ini`，包含所有相机的IP配置。

## 技术实现细节

### IP地址匹配机制
1. 枚举所有3D相机设备
2. 将设备的IP地址从UInt32格式转换为字符串格式
3. 与目标IP地址进行比较
4. 找到匹配的设备后直接连接

### 设备索引管理
- 使用 `m_nCurrentDeviceIndex` 保存当前连接设备在设备列表中的索引
- 在 `ReceiveThreadProcess` 中使用此索引访问设备信息
- 避免了因IP过滤导致的索引不匹配问题

### 错误处理
- 添加了设备未找到的错误提示
- 保持了与原有错误处理机制的一致性

## 使用方法

### 配置文件设置
在 `camera_config.ini` 文件中添加C4相机的IP配置：
```ini
[CameraIP]
C4=*************
```

### 网络要求
- 确保C4 3D相机和PC在同一网段
- 相机IP地址需要正确配置
- 网络连接稳定

## 兼容性
- 保持了与原有代码的兼容性
- 不影响C1、C2、C3相机的连接功能
- 支持原有的枚举连接方式作为备选

## 测试建议
1. 验证IP地址连接功能
2. 测试重新连接功能
3. 确认3D图像采集正常
4. 验证配置文件读取正确

## 注意事项
- 确保相机IP地址配置正确
- 网络环境需要稳定
- 如果IP连接失败，可以检查网络连接和IP配置
