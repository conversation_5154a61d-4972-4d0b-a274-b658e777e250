﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="$this.Text" xml:space="preserve">
    <value>视觉检测系统</value>
  </data>
  <data name="alreadyExit" xml:space="preserve">
    <value>目录中包含此项</value>
  </data>
  <data name="barButtonItem4.Caption" xml:space="preserve">
    <value>注册</value>
  </data>
  <data name="barButtonItem6.Caption" xml:space="preserve">
    <value>程序帮助</value>
  </data>
  <data name="barButtonItem7.Caption" xml:space="preserve">
    <value>问题反馈</value>
  </data>
  <data name="Btm_Connect.Caption" xml:space="preserve">
    <value>连接相机</value>
  </data>
  <data name="Btm_ConnectDatabase.Caption" xml:space="preserve">
    <value>连接数据库</value>
  </data>
  <data name="Btm_DataReport.Caption" xml:space="preserve">
    <value>数据报表</value>
  </data>
  <data name="Btm_DisConnect.Caption" xml:space="preserve">
    <value>断开连接</value>
  </data>
  <data name="Btm_FilePath.Caption" xml:space="preserve">
    <value>保存路径</value>
  </data>
  <data name="Btm_FindTemplate.Caption" xml:space="preserve">
    <value>模板匹配</value>
  </data>
  <data name="Btm_ImageDown.Caption" xml:space="preserve">
    <value>图片下载</value>
  </data>
  <data name="Btm_LoadRecipe.Caption" xml:space="preserve">
    <value>工艺导入</value>
  </data>
  <data name="Btm_Log.Caption" xml:space="preserve">
    <value>日志</value>
  </data>
  <data name="Btm_Record.Caption" xml:space="preserve">
    <value>数据记录</value>
  </data>
  <data name="Btm_refesh.Caption" xml:space="preserve">
    <value>信息更新</value>
  </data>
  <data name="Btm_Register.Caption" xml:space="preserve">
    <value>软件注册</value>
  </data>
  <data name="Btm_S7.Caption" xml:space="preserve">
    <value>西门子S7</value>
  </data>
  <data name="Btm_SetPara.Caption" xml:space="preserve">
    <value>参数设置</value>
  </data>
  <data name="btn_circlecaliper.Caption" xml:space="preserve">
    <value>测量工具</value>
  </data>
  <data name="Btn_Mitsubishi.Caption" xml:space="preserve">
    <value>三菱通讯</value>
  </data>
  <data name="C1_connected" xml:space="preserve">
    <value>相机C1连接成功！</value>
  </data>
  <data name="C1_disconnected" xml:space="preserve">
    <value>相机C1打开失败!</value>
  </data>
  <data name="C1_open" xml:space="preserve">
    <value>相机C1打开</value>
  </data>
  <data name="C2_connected" xml:space="preserve">
    <value>相机C2连接成功！</value>
  </data>
  <data name="C2_disconnected" xml:space="preserve">
    <value>相机C2打开失败!</value>
  </data>
  <data name="C2_open" xml:space="preserve">
    <value>相机C2打开</value>
  </data>
  <data name="C3_connected" xml:space="preserve">
    <value>相机C2连接成功！</value>
  </data>
  <data name="C3_disconnected" xml:space="preserve">
    <value>相机C3打开失败!</value>
  </data>
  <data name="C3_open" xml:space="preserve">
    <value>相机C3打开</value>
  </data>
  <data name="capstop" xml:space="preserve">
    <value>帽止口检测失败</value>
  </data>
  <data name="chooseContent" xml:space="preserve">
    <value>选择保存目录</value>
  </data>
  <data name="database_con" xml:space="preserve">
    <value>连接数据库</value>
  </data>
  <data name="database_discon" xml:space="preserve">
    <value>断开数据库</value>
  </data>
  <data name="datasend_fault" xml:space="preserve">
    <value>数据发送失败，请检查映射表中有无此型号</value>
  </data>
  <data name="disconnected" xml:space="preserve">
    <value>连接已断开</value>
  </data>
  <data name="Edit_Databasename.Caption" xml:space="preserve">
    <value>数据库名称</value>
  </data>
  <data name="en_barStaticItem.Caption" xml:space="preserve">
    <value>英文</value>
  </data>
  <data name="es_barStaticItem.Caption" xml:space="preserve">
    <value>西班牙语</value>
  </data>
  <data name="file" xml:space="preserve">
    <value>文件</value>
  </data>
  <data name="fileNotExit" xml:space="preserve">
    <value>保存文件不存在</value>
  </data>
  <data name="fileOpened" xml:space="preserve">
    <value>"文件被打开,请关闭文件，重新加载"</value>
  </data>
  <data name="groupControl1.Text" xml:space="preserve">
    <value>信息窗口</value>
  </data>
  <data name="hole_dection" xml:space="preserve">
    <value>螺栓孔检测失败</value>
  </data>
  <data name="hubNotEnter" xml:space="preserve">
    <value>未录入此款轮毂的找孔区域</value>
  </data>
  <data name="imgDirOnload" xml:space="preserve">
    <value>请加载图片文件夹！</value>
  </data>
  <data name="importFailed" xml:space="preserve">
    <value>导入文件失败，找不到</value>
  </data>
  <data name="label_Camera1status.Caption" xml:space="preserve">
    <value>相机C1连接状态</value>
  </data>
  <data name="label_Camera2status.Caption" xml:space="preserve">
    <value>相机C2连接状态</value>
  </data>
  <data name="label_Camera3status.Caption" xml:space="preserve">
    <value>相机C3连接状态</value>
  </data>
  <data name="label_CountC1.Caption" xml:space="preserve">
    <value>C1数量:</value>
  </data>
  <data name="label_CountC2.Caption" xml:space="preserve">
    <value>C2数量:</value>
  </data>
  <data name="label_CountC3.Caption" xml:space="preserve">
    <value>C3数量:</value>
  </data>
  <data name="label_DBStatus.Caption" xml:space="preserve">
    <value>数据库状态</value>
  </data>
  <data name="label_Systermtime.Caption" xml:space="preserve">
    <value>系统时间：</value>
  </data>
  <data name="labelControl1.Text" xml:space="preserve">
    <value>相机名称</value>
  </data>
  <data name="lackTable" xml:space="preserve">
    <value>缺少可用表格</value>
  </data>
  <data name="menu_Language.Caption" xml:space="preserve">
    <value>切换语言</value>
  </data>
  <data name="noCamera" xml:space="preserve">
    <value>无C1、C2、C3相机</value>
  </data>
  <data name="noColor" xml:space="preserve">
    <value>无</value>
  </data>
  <data name="num" xml:space="preserve">
    <value>数量：</value>
  </data>
  <data name="param" xml:space="preserve">
    <value>无参数配方</value>
  </data>
  <data name="param_hole" xml:space="preserve">
    <value>无参数配方或螺栓孔配方缺失</value>
  </data>
  <data name="PLC_disconnected" xml:space="preserve">
    <value>PLC通讯失败</value>
  </data>
  <data name="PLC_disconnected1" xml:space="preserve">
    <value>PLC断开连接</value>
  </data>
  <data name="ribbonPage1.Text" xml:space="preserve">
    <value>视觉</value>
  </data>
  <data name="ribbonPage2.Text" xml:space="preserve">
    <value>数据</value>
  </data>
  <data name="ribbonPage3.Text" xml:space="preserve">
    <value>相机</value>
  </data>
  <data name="ribbonPage4.Text" xml:space="preserve">
    <value>通讯</value>
  </data>
  <data name="ribbonPage5.Text" xml:space="preserve">
    <value>帮助</value>
  </data>
  <data name="sure" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="alreadyRe" xml:space="preserve">
    <value>此软件已注册！</value>
  </data>
  <data name="noRegister" xml:space="preserve">
    <value>此软件尚未注册！</value>
  </data>
  <data name="testVer" xml:space="preserve">
    <value>您现在使用的是试用版，可以免费试用5次！</value>
  </data>
  <data name="message" xml:space="preserve">
    <value>信息</value>
  </data>
  <data name="alreadytimes" xml:space="preserve">
    <value>您已经使用了</value>
  </data>
  <data name="times" xml:space="preserve">
    <value>次！</value>
  </data>
  <data name="welcome" xml:space="preserve">
    <value>欢迎使用本软件！</value>
  </data>
  <data name="mosttimes" xml:space="preserve">
    <value>试用次数已到！您是否需要注册？</value>
  </data>
  <data name="zh_barStaticItem.Caption" xml:space="preserve">
    <value>中文(简体)</value>
  </data>
  <data name="alreadyRe" xml:space="preserve">
    <value>此软件已注册！</value>
  </data>
  <data name="noRegister" xml:space="preserve">
    <value>此软件尚未注册！</value>
  </data>
  <data name="testVer" xml:space="preserve">
    <value>您现在使用的是试用版，可以免费试用5次！</value>
  </data>
  <data name="message" xml:space="preserve">
    <value>信息</value>
  </data>
  <data name="alreadytimes" xml:space="preserve">
    <value>您已经使用了</value>
  </data>
  <data name="welcome" xml:space="preserve">
    <value>欢迎使用本软件！</value>
  </data>
  <data name="mosttimes" xml:space="preserve">
    <value>试用次数已到！您是否需要注册？</value>
  </data>
</root>