﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="enumFailed" xml:space="preserve">
    <value>枚举设备失败</value>
  </data>
  <data name="leftChoose" xml:space="preserve">
    <value>请用左键选择</value>
  </data>
  <data name="noDevice" xml:space="preserve">
    <value>没有设备，请选择</value>
  </data>
  <data name="notArea" xml:space="preserve">
    <value>未生成区域！</value>
  </data>
  <data name="openFailed" xml:space="preserve">
    <value>打开设备识别</value>
  </data>
  <data name="selectIRO" xml:space="preserve">
    <value>请框选IRO</value>
  </data>
  <data name="serGainFail" xml:space="preserve">
    <value>设置增益失败!</value>
  </data>
  <data name="setFrameFail" xml:space="preserve">
    <value>设置帧率失败!</value>
  </data>
  <data name="setTimeFail" xml:space="preserve">
    <value>设置曝光时间失败!</value>
  </data>
  <data name="startGrabFail" xml:space="preserve">
    <value>开始捕获失败</value>
  </data>
  <data name="stopGrabFail" xml:space="preserve">
    <value>结束捕获失败</value>
  </data>
  <data name="sure" xml:space="preserve">
    <value>确定</value>
  </data>
  <data name="triggerFail" xml:space="preserve">
    <value>触发失败</value>
  </data>
  <data name="cancel" xml:space="preserve">
    <value>取消</value>
  </data>
  <data name="databaseFault" xml:space="preserve">
    <value>数据库连接失败</value>
  </data>
  <data name="faultPaw" xml:space="preserve">
    <value>密码不正确</value>
  </data>
  <data name="loginSuccess" xml:space="preserve">
    <value>登录成功</value>
  </data>
  <data name="datatype" xml:space="preserve">
    <value>数据类型不匹配</value>
  </data>
  <data name="savesuccessfully" xml:space="preserve">
    <value>保存成功</value>
  </data>
  <data name="fileNotExit" xml:space="preserve">
    <value>文件不存在</value>
  </data>
  <data name="notRecord" xml:space="preserve">
    <value>模板缺失</value>
  </data>
  <data name="dbNotOpen" xml:space="preserve">
    <value>数据库未打开</value>
  </data>
  <data name="saveFault" xml:space="preserve">
    <value>保存失败</value>
  </data>
  <data name="saveCen" xml:space="preserve">
    <value>中心孔保存成功</value>
  </data>
  <data name="noImgOrType" xml:space="preserve">
    <value>加载图像或类型</value>
  </data>
  <data name="noParamOrHub" xml:space="preserve">
    <value>缺少配方</value>
  </data>
  <data name="boltHoleSave" xml:space="preserve">
    <value>螺栓孔保存成功</value>
  </data>
  <data name="saveCap" xml:space="preserve">
    <value>帽止口保存成功</value>
  </data>
  <data name="paramExit" xml:space="preserve">
    <value>参数存在，是否替换</value>
  </data>
  <data name="lacktable" xml:space="preserve">
    <value>缺少可用表格</value>
  </data>
  <data name="fileopenClose" xml:space="preserve">
    <value>"文件被打开,请关闭文件，重新加载"</value>
  </data>
  <data name="tableisempty" xml:space="preserve">
    <value>表格内容为空</value>
  </data>
  <data name="alreadyRe" xml:space="preserve">
    <value>软件已经注册</value>
  </data>
  <data name="modifySucceeded" xml:space="preserve">
    <value>修改成功</value>
  </data>
  <data name="passwordEmpty" xml:space="preserve">
    <value>新密码不能为空</value>
  </data>
  <data name="passwordInconsistency" xml:space="preserve">
    <value>两次输入的新密码不一致，请重新输入！</value>
  </data>
</root>