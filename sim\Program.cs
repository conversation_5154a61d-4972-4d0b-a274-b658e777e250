﻿using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Resources;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Drawing;
using Microsoft.Win32;

namespace BingSolhalcon
{

    static class Program
    {
        /// <summary>
        /// 应用程序的主入口点。
        /// </summary>
        [STAThread]

        static void Main()
        {
            try
            {

                #region 资源文件生成

                DirectoryInfo path_string = new DirectoryInfo(Application.StartupPath);
                string filename = path_string.FullName;
                DataTable csv_dt = new DataTable();
                string filePath = path_string + "\\resources\\resource.csv";
                csv_dt = CsvLib.Csv2dt(filePath, 0);
                int rowCount = csv_dt.Rows.Count;
                // Properties.Settings.Default.DefaultLanguage = "zn";
                string language = Properties.Settings.Default.DefaultLanguage;
                System.Threading.Thread.CurrentThread.CurrentUICulture = new System.Globalization.CultureInfo(language);
                //  DataView dv = csv_dt.DefaultView;
                // DataTable forms = dv.ToTable(true, new string[] { "form" });
                //拼接重复数据及重复数据出现的索引
                Dictionary<string, List<int>> all_data = new Dictionary<string, List<int>>();

                //取出重复数据
                var dis_data = (from a in csv_dt.AsEnumerable()
                                group a by a.Field<string>("form")
                            into g
                                where g.Count() > 1
                                select new
                                {
                                    value = g.Key
                                }).ToList();

                if (dis_data.Count > 0)
                {
                    for (int j = 0; j < dis_data.Count; j++)
                    {
                        List<int> now_index = new List<int>();//出现重复的数据Index
                        for (int i = 0; i < csv_dt.Rows.Count; i++)
                        {
                            if (csv_dt.Rows[i]["form"].ToString() == dis_data[j].value)
                            {
                                now_index.Add(i + 2);//输出行数
                            }
                        }
                        all_data.Add(dis_data[j].value, now_index);
                    }
                }

                ResXResourceWriter rw_en;
                ResXResourceWriter rw_es;
                ResXResourceWriter rw_zh;
                ResXResourceWriter rw_fr;
                //遍历key
                foreach (string key in all_data.Keys)
                {
                    //if (key == "nonUIresx")
                    //{
                    rw_en = new ResXResourceWriter(path_string + "\\resources\\" + key + ".en.resx");
                    rw_es = new ResXResourceWriter(path_string + "\\resources\\" + key + ".es.resx");
                    rw_zh = new ResXResourceWriter(path_string + "\\resources\\" + key + ".zh.resx");
                    rw_fr = new ResXResourceWriter(path_string + "\\resources\\" + key + ".fr.resx");
                    //}
                    //else
                    //{
                    //    rw_en = new ResXResourceWriter(path_string + "\\UI\\" + key + ".en.resx");
                    //    rw_es = new ResXResourceWriter(path_string + "\\UI\\" + key + ".es.resx");
                    //    rw_zh = new ResXResourceWriter(path_string + "\\UI\\" + key + ".zh.resx");

                    //    //更新资源文件(或者将debug/UI中的文件替换UI中的资源文件
                    //    //rw_en = new ResXResourceWriter(filename + "\\UI\\" + key + ".en.resx");
                    //    //rw_es = new ResXResourceWriter(filename + "\\UI\\" + key + ".es.resx");
                    //    //rw_zh = new ResXResourceWriter(filename + "\\UI\\" + key + ".zh.resx");
                    //}

                    foreach (var i in all_data[key])
                    {
                        string name = csv_dt.Rows[i - 2]["text"].ToString();
                        string chinese = csv_dt.Rows[i - 2]["Chinese"].ToString();
                        string english = csv_dt.Rows[i - 2]["English"].ToString();
                        string spanish = csv_dt.Rows[i - 2]["Spanish"].ToString();
                        string french = csv_dt.Rows[i - 2]["French"].ToString();
                        rw_zh.AddResource(name, chinese);
                        rw_en.AddResource(name, english);
                        rw_es.AddResource(name, spanish);
                        rw_fr.AddResource(name, french);
                    }
                    rw_en.Generate();
                    rw_es.Generate();
                    rw_zh.Generate();
                    rw_fr.Generate();
                    rw_en.Close();
                    rw_es.Close();
                    rw_zh.Close();
                    rw_fr.Close();
                }
                #endregion


                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);


                Form1 form1 = new Form1();
                Application.Run(form1);
            }
            catch (Exception e)
            {
                MessageBox.Show(e.Message + "\n\n" + e.StackTrace, "", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

        }
    }
}
