﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{450644B8-B7B9-434E-8359-F404D8D31C94}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>HWindow_Tool</RootNamespace>
    <AssemblyName>HWindow_Tool</AssemblyName>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="halcondotnet">
      <HintPath>..\..\..\..\..\..\视觉\巴斯勒&amp;VISIONPRO\C#_x64_Pylon5_Halcon10_VS2010_2CCD_V4.0\PylonLiveViewer\PylonLiveViewer\bin\x64\Debug\halcondotnet.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Runtime.Serialization.Formatters.Soap" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Config\Circle.cs" />
    <Compile Include="Config\CircularArc.cs" />
    <Compile Include="Config\HObjectWithColor.cs" />
    <Compile Include="Config\Line.cs" />
    <Compile Include="Config\Rectangle1.cs" />
    <Compile Include="Config\Rectangle2.cs" />
    <Compile Include="Config\SerializeHelper.cs" />
    <Compile Include="HWindow_Final.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="HWindow_Final.designer.cs" />
    <Compile Include="Model\GraphicsContext.cs" />
    <Compile Include="Model\HObjectEntry.cs" />
    <Compile Include="Model\HWndCtrl.cs" />
    <Compile Include="Model\IViewWindow.cs" />
    <Compile Include="Model\ROI.cs" />
    <Compile Include="Model\ROICircle.cs" />
    <Compile Include="Model\ROICircularArc.cs" />
    <Compile Include="Model\ROIController.cs" />
    <Compile Include="Model\RoiData.cs" />
    <Compile Include="Model\ROILine.cs" />
    <Compile Include="Model\ROINurbs.cs" />
    <Compile Include="Model\ROIRectangle1.cs" />
    <Compile Include="Model\ROIRectangle2.cs" />
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="ViewWindow.cs" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="HWindow_Final.resx">
      <DependentUpon>HWindow_Final.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>