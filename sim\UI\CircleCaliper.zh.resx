﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="$this.Text" xml:space="preserve">
    <value>CircleCaliper</value>
  </data>
  <data name="btn_drawcircle.Text" xml:space="preserve">
    <value>画测量圆</value>
  </data>
  <data name="btn_mtrsave.Text" xml:space="preserve">
    <value>保存</value>
  </data>
  <data name="btn_readimage.Text" xml:space="preserve">
    <value>载入图片</value>
  </data>
  <data name="btn_test.Text" xml:space="preserve">
    <value>测试</value>
  </data>
  <data name="comboBox_selecttype.AutoCompleteCustomSource" xml:space="preserve">
    <value>轮型识别</value>
  </data>
  <data name="comboBox_selecttype.AutoCompleteCustomSource1" xml:space="preserve">
    <value>中心孔</value>
  </data>
  <data name="comboBox_selecttype.AutoCompleteCustomSource2" xml:space="preserve">
    <value>气门孔</value>
  </data>
  <data name="comboBox_selecttype.Items" xml:space="preserve">
    <value>中心孔</value>
  </data>
  <data name="comboBox_selecttype.Items1" xml:space="preserve">
    <value>螺栓孔</value>
  </data>
  <data name="comboBox_selecttype.Items2" xml:space="preserve">
    <value>帽止口</value>
  </data>
  <data name="comboBox_selecttype.Text" xml:space="preserve">
    <value>中心孔</value>
  </data>
  <data name="groupBox2.Text" xml:space="preserve">
    <value>中心孔参数设置</value>
  </data>
  <data name="groupBox3.Text" xml:space="preserve">
    <value>螺栓孔参数设置</value>
  </data>
  <data name="groupBox5.Text" xml:space="preserve">
    <value>帽止口参数设置</value>
  </data>
  <data name="label1.Text" xml:space="preserve">
    <value>边序选择</value>
  </data>
  <data name="label11.Text" xml:space="preserve">
    <value>平滑度</value>
  </data>
  <data name="label12.Text" xml:space="preserve">
    <value>最小分值</value>
  </data>
  <data name="label13.Text" xml:space="preserve">
    <value>边序选择</value>
  </data>
  <data name="label14.Text" xml:space="preserve">
    <value>极性</value>
  </data>
  <data name="label15.Text" xml:space="preserve">
    <value>测量区域数量</value>
  </data>
  <data name="label16.Text" xml:space="preserve">
    <value>测量矩形长</value>
  </data>
  <data name="label17.Text" xml:space="preserve">
    <value>测量矩形宽</value>
  </data>
  <data name="label18.Text" xml:space="preserve">
    <value>样轮直径</value>
  </data>
  <data name="label19.Text" xml:space="preserve">
    <value>模板类型</value>
  </data>
  <data name="label2.Text" xml:space="preserve">
    <value>最小分值</value>
  </data>
  <data name="label20.Text" xml:space="preserve">
    <value>样轮小孔直径</value>
  </data>
  <data name="label22.Text" xml:space="preserve">
    <value>最小灰度</value>
  </data>
  <data name="label23.Text" xml:space="preserve">
    <value>最小灰度</value>
  </data>
  <data name="label24.Text" xml:space="preserve">
    <value>平滑度</value>
  </data>
  <data name="label25.Text" xml:space="preserve">
    <value>最小分值</value>
  </data>
  <data name="label26.Text" xml:space="preserve">
    <value>边序选择</value>
  </data>
  <data name="label27.Text" xml:space="preserve">
    <value>极性</value>
  </data>
  <data name="label28.Text" xml:space="preserve">
    <value>样轮直径</value>
  </data>
  <data name="label29.Text" xml:space="preserve">
    <value>测量区域数量</value>
  </data>
  <data name="label3.Text" xml:space="preserve">
    <value>轮毂型号</value>
  </data>
  <data name="label30.Text" xml:space="preserve">
    <value>测量矩形长</value>
  </data>
  <data name="label31.Text" xml:space="preserve">
    <value>测量矩形宽</value>
  </data>
  <data name="label4.Text" xml:space="preserve">
    <value>最小灰度</value>
  </data>
  <data name="label5.Text" xml:space="preserve">
    <value>测量矩形宽</value>
  </data>
  <data name="label6.Text" xml:space="preserve">
    <value>平滑度</value>
  </data>
  <data name="label7.Text" xml:space="preserve">
    <value>测量矩形长</value>
  </data>
  <data name="label8.Text" xml:space="preserve">
    <value>测量区域数量</value>
  </data>
  <data name="label9.Text" xml:space="preserve">
    <value>极性</value>
  </data>
  <data name="allFile" xml:space="preserve">
    <value>所有文件(*.*)|*.*</value>
  </data>
  <data name="boltHoleDecFail" xml:space="preserve">
    <value>螺栓孔检测失败</value>
  </data>
  <data name="boltHoleLack" xml:space="preserve">
    <value>无参数配方或螺栓孔配方缺失</value>
  </data>
  <data name="boltHoleSave" xml:space="preserve">
    <value>螺栓孔模型保存成功</value>
  </data>
  <data name="dbNotOpen" xml:space="preserve">
    <value>数据库未打开</value>
  </data>
  <data name="hubExit" xml:space="preserve">
    <value>此轮毂已经录入，是否替换</value>
  </data>
  <data name="loadImg" xml:space="preserve">
    <value>请加载图像</value>
  </data>
  <data name="loadImg1" xml:space="preserve">
    <value>请加载一张图片</value>
  </data>
  <data name="noImgOrType" xml:space="preserve">
    <value>未加载图片或未输入轮型号</value>
  </data>
  <data name="noParam" xml:space="preserve">
    <value>无参数配方</value>
  </data>
  <data name="noParamOrHub" xml:space="preserve">
    <value>配方未创建或轮毂参数表不存在</value>
  </data>
  <data name="notRecordCap" xml:space="preserve">
    <value>未录入帽止口孔模板</value>
  </data>
  <data name="notRecordCen" xml:space="preserve">
    <value>未录入中心孔模板</value>
  </data>
  <data name="paramExit" xml:space="preserve">
    <value>此轮毂中心孔参数已存在，是否增添螺栓孔参数</value>
  </data>
  <data name="saveCap" xml:space="preserve">
    <value>帽止口模型保存成功</value>
  </data>
  <data name="saveCen" xml:space="preserve">
    <value>中心孔模型保存成功</value>
  </data>
  <data name="saveFault" xml:space="preserve">
    <value>保存失败！</value>
  </data>
  <data name="selectFile" xml:space="preserve">
    <value>请选择文件夹</value>
  </data>
  <data name="sure" xml:space="preserve">
    <value>OK</value>
  </data>
</root>