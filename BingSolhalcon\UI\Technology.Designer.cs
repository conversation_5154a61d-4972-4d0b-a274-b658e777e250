﻿
namespace BingSolhalcon.UI
{
    partial class Technology
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(Technology));
            this.ribbonControl1 = new DevExpress.XtraBars.Ribbon.RibbonControl();
            this.imageCollection1 = new DevExpress.Utils.ImageCollection(this.components);
            this.Btn_ImportExc = new DevExpress.XtraBars.BarButtonItem();
            this.Btn_ExportExc = new DevExpress.XtraBars.BarButtonItem();
            this.Btn_LoadTechnology = new DevExpress.XtraBars.BarButtonItem();
            this.barEditItem1 = new DevExpress.XtraBars.BarEditItem();
            this.repositoryItemRichTextEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemRichTextEdit();
            this.Edt_Databasename = new DevExpress.XtraBars.BarEditItem();
            this.repositoryItemRichTextEdit2 = new DevExpress.XtraEditors.Repository.RepositoryItemRichTextEdit();
            this.Btn_File = new DevExpress.XtraBars.BarButtonItem();
            this.ribbonPage1 = new DevExpress.XtraBars.Ribbon.RibbonPage();
            this.ribbonPageGroup1 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroup2 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.GridView_Technology = new System.Windows.Forms.DataGridView();
            this.zoomTrackBarControl1 = new DevExpress.XtraEditors.ZoomTrackBarControl();
            ((System.ComponentModel.ISupportInitialize)(this.ribbonControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.imageCollection1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemRichTextEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemRichTextEdit2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.GridView_Technology)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.zoomTrackBarControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.zoomTrackBarControl1.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // ribbonControl1
            // 
            this.ribbonControl1.ExpandCollapseItem.Id = 0;
            this.ribbonControl1.Images = this.imageCollection1;
            this.ribbonControl1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.ribbonControl1.ExpandCollapseItem,
            this.Btn_ImportExc,
            this.Btn_ExportExc,
            this.Btn_LoadTechnology,
            this.barEditItem1,
            this.Edt_Databasename,
            this.Btn_File});
            this.ribbonControl1.LargeImages = this.imageCollection1;
            resources.ApplyResources(this.ribbonControl1, "ribbonControl1");
            this.ribbonControl1.MaxItemId = 8;
            this.ribbonControl1.Name = "ribbonControl1";
            this.ribbonControl1.Pages.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPage[] {
            this.ribbonPage1});
            this.ribbonControl1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemRichTextEdit1,
            this.repositoryItemRichTextEdit2});
            // 
            // imageCollection1
            // 
            resources.ApplyResources(this.imageCollection1, "imageCollection1");
            this.imageCollection1.ImageStream = ((DevExpress.Utils.ImageCollectionStreamer)(resources.GetObject("imageCollection1.ImageStream")));
            this.imageCollection1.Images.SetKeyName(0, "09.png");
            this.imageCollection1.Images.SetKeyName(1, "14.png");
            this.imageCollection1.Images.SetKeyName(2, "download.png");
            // 
            // Btn_ImportExc
            // 
            resources.ApplyResources(this.Btn_ImportExc, "Btn_ImportExc");
            this.Btn_ImportExc.Id = 1;
            this.Btn_ImportExc.ImageIndex = 1;
            this.Btn_ImportExc.LargeImageIndex = 1;
            this.Btn_ImportExc.Name = "Btn_ImportExc";
            this.Btn_ImportExc.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.Btn_ImportExc_ItemClick);
            // 
            // Btn_ExportExc
            // 
            resources.ApplyResources(this.Btn_ExportExc, "Btn_ExportExc");
            this.Btn_ExportExc.Id = 2;
            this.Btn_ExportExc.ImageIndex = 0;
            this.Btn_ExportExc.LargeImageIndex = 0;
            this.Btn_ExportExc.Name = "Btn_ExportExc";
            this.Btn_ExportExc.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.Btn_ExportExc_ItemClick);
            // 
            // Btn_LoadTechnology
            // 
            resources.ApplyResources(this.Btn_LoadTechnology, "Btn_LoadTechnology");
            this.Btn_LoadTechnology.Id = 3;
            this.Btn_LoadTechnology.ImageIndex = 2;
            this.Btn_LoadTechnology.LargeImageIndex = 2;
            this.Btn_LoadTechnology.Name = "Btn_LoadTechnology";
            this.Btn_LoadTechnology.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.Btn_LoadTechnology_ItemClick);
            // 
            // barEditItem1
            // 
            resources.ApplyResources(this.barEditItem1, "barEditItem1");
            this.barEditItem1.Edit = this.repositoryItemRichTextEdit1;
            this.barEditItem1.Id = 5;
            this.barEditItem1.Name = "barEditItem1";
            // 
            // repositoryItemRichTextEdit1
            // 
            this.repositoryItemRichTextEdit1.Name = "repositoryItemRichTextEdit1";
            this.repositoryItemRichTextEdit1.ShowCaretInReadOnly = false;
            // 
            // Edt_Databasename
            // 
            resources.ApplyResources(this.Edt_Databasename, "Edt_Databasename");
            this.Edt_Databasename.Edit = this.repositoryItemRichTextEdit2;
            this.Edt_Databasename.Id = 6;
            this.Edt_Databasename.Name = "Edt_Databasename";
            // 
            // repositoryItemRichTextEdit2
            // 
            this.repositoryItemRichTextEdit2.Name = "repositoryItemRichTextEdit2";
            this.repositoryItemRichTextEdit2.ShowCaretInReadOnly = false;
            // 
            // Btn_File
            // 
            resources.ApplyResources(this.Btn_File, "Btn_File");
            this.Btn_File.Id = 7;
            this.Btn_File.Name = "Btn_File";
            // 
            // ribbonPage1
            // 
            this.ribbonPage1.Groups.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPageGroup[] {
            this.ribbonPageGroup1,
            this.ribbonPageGroup2});
            this.ribbonPage1.Name = "ribbonPage1";
            resources.ApplyResources(this.ribbonPage1, "ribbonPage1");
            // 
            // ribbonPageGroup1
            // 
            this.ribbonPageGroup1.ItemLinks.Add(this.Btn_ImportExc);
            this.ribbonPageGroup1.Name = "ribbonPageGroup1";
            // 
            // ribbonPageGroup2
            // 
            this.ribbonPageGroup2.ItemLinks.Add(this.Btn_ExportExc);
            this.ribbonPageGroup2.ItemLinks.Add(this.Btn_LoadTechnology);
            this.ribbonPageGroup2.Name = "ribbonPageGroup2";
            // 
            // GridView_Technology
            // 
            resources.ApplyResources(this.GridView_Technology, "GridView_Technology");
            this.GridView_Technology.BackgroundColor = System.Drawing.Color.FromArgb(((int)(((byte)(207)))), ((int)(((byte)(221)))), ((int)(((byte)(238)))));
            this.GridView_Technology.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.GridView_Technology.Name = "GridView_Technology";
            this.GridView_Technology.RowTemplate.Height = 23;
            // 
            // zoomTrackBarControl1
            // 
            resources.ApplyResources(this.zoomTrackBarControl1, "zoomTrackBarControl1");
            this.zoomTrackBarControl1.MenuManager = this.ribbonControl1;
            this.zoomTrackBarControl1.Name = "zoomTrackBarControl1";
            this.zoomTrackBarControl1.Properties.Middle = 5;
            this.zoomTrackBarControl1.Properties.ScrollThumbStyle = DevExpress.XtraEditors.Repository.ScrollThumbStyle.ArrowDownRight;
            this.zoomTrackBarControl1.EditValueChanged += new System.EventHandler(this.zoomTrackBarControl1_EditValueChanged);
            // 
            // Technology
            // 
            this.Appearance.Options.UseFont = true;
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.zoomTrackBarControl1);
            this.Controls.Add(this.GridView_Technology);
            this.Controls.Add(this.ribbonControl1);
            this.Cursor = System.Windows.Forms.Cursors.Default;
            this.Name = "Technology";
            this.Ribbon = this.ribbonControl1;
            this.FormClosed += new System.Windows.Forms.FormClosedEventHandler(this.FormClose);
            this.Load += new System.EventHandler(this.FormLoad);
            ((System.ComponentModel.ISupportInitialize)(this.ribbonControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.imageCollection1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemRichTextEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemRichTextEdit2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.GridView_Technology)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.zoomTrackBarControl1.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.zoomTrackBarControl1)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion
        private DevExpress.XtraBars.Ribbon.RibbonControl ribbonControl1;
        private DevExpress.XtraBars.Ribbon.RibbonPage ribbonPage1;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup1;
        private DevExpress.XtraBars.BarButtonItem Btn_ImportExc;
        private DevExpress.XtraBars.BarButtonItem Btn_ExportExc;
        private DevExpress.Utils.ImageCollection imageCollection1;
        private System.Windows.Forms.DataGridView GridView_Technology;
        private DevExpress.XtraBars.BarButtonItem Btn_LoadTechnology;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup2;
        private DevExpress.XtraBars.BarButtonItem barButtonItem1;
        private DevExpress.XtraBars.BarEditItem barEditItem1;
        private DevExpress.XtraEditors.Repository.RepositoryItemRichTextEdit repositoryItemRichTextEdit1;
        private DevExpress.XtraBars.BarEditItem Edt_Databasename;
        private DevExpress.XtraEditors.Repository.RepositoryItemRichTextEdit repositoryItemRichTextEdit2;
        private DevExpress.XtraBars.BarButtonItem Btn_File;
        private DevExpress.XtraEditors.ZoomTrackBarControl zoomTrackBarControl1;
    }
}