﻿using System;
using System.IO;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using HalconDotNet;
using System.Resources;
using BingSolhalcon.resources;
using System.Threading.Tasks;
using System.Threading;
using System.Collections.Concurrent;

namespace BingSolhalcon
{
    public partial class Patterm : Form
    {
        // Local iconic variables 
        public delegate void delegateGetDatabasename(ref string databasename,ref string numberofdecimal);
        public event delegateGetDatabasename evenGetDatabasename;
        string m_databasename, numberofdecimal;
        string connection;



        private float x;//定义当前窗体的宽度
        private float y;//定义当前窗体的高度
        private OpenFileDialog dialog;
        private SaveFileDialog SaveFile;
        private string path;  //文件保存路径
        private int thresholdvalue;    //灰度阀值
        private bool resetorigin = false;//是否重设中心点
        private  hwindows.hwindows  m_halconwindow;
        public const int CAPTURE_REGION = 10;

        // Local iconic variables 

        public static ResourceManager res = new ResourceManager(typeof(nonUIresx)); //自定义资源字段


        HObject ho_Image, ho_ImageReduced, ho_SelectedContours, ho_OEmptyObject1, templateRegion;

        HObjectVector hvec_OEmptyObject = new HObjectVector(1);
        //绘图用变量


        // Local control variables 

        HTuple hv_ModelID3 = null;
        HTuple hv_width = null, hv_height = null, RowDown = null, ColDown;
        HTuple hv_Rowleft=null,hv_Columnleft=null,  hv_Rowright=null,hv_Columnright=null;
        HTuple hv_ModelOriginRow = null, hv_ModelOriginColumn = null;
        HObject ho_resetcross,ho_resetrectangle;
            

        private static Patterm _instance;
        public static Patterm Instance
        {
            get
            {
                if (_instance == null)
                    _instance = new Patterm();
                return _instance;
            }
        }

        //****************数据类型******************************//
        struct fileinfor
        {
            public string filename, wheelsize;

        }
        public enum wheelsize
        {
            inch14, inch15, inch16, inch17, inch18, inch19, inch20, inch21, inch22, inch23, inch24, outrang

        }
        private wheelsize Pixelconversion(HTuple size)
        {
            double[] hubsize = new double[10];
            hubsize = size.ToDArr();
            wheelsize numwheelsize = wheelsize.outrang;
            if (500 < hubsize[0] && hubsize[0] < 1000)
            {
                if (290 < hubsize[0] && hubsize[0] < 310)
                    numwheelsize = wheelsize.inch14;

                if (290 < hubsize[0] && hubsize[0] < 310)
                    numwheelsize = wheelsize.inch15;

                if (550 < hubsize[0] && hubsize[0] < 590)//
                    numwheelsize = wheelsize.inch16;
                if (590 < hubsize[0] && hubsize[0] < 640)//
                    numwheelsize = wheelsize.inch17;
                if (640 <= hubsize[0] && hubsize[0] < 690)
                    numwheelsize = wheelsize.inch18;

                if (690 <= hubsize[0] && hubsize[0] < 740)
                    numwheelsize = wheelsize.inch19;

                if (740 < hubsize[0] && hubsize[0] < 780)
                    numwheelsize = wheelsize.inch20;

                if (290 < hubsize[0] && hubsize[0] < 310)
                    numwheelsize = wheelsize.inch21;

                if (290 < hubsize[0] && hubsize[0] < 310)
                    numwheelsize = wheelsize.inch22;
            }
            return numwheelsize;


        }


        //轮毂尺寸筛选
     
        // Procedures 
        // External procedures 
        // Chapter: Filters / Arithmetic
        // Short Description: Scale the gray values of an image from the interval [Min,Max] to [0,255] 
        public void scale_image_range(HObject ho_Image, out HObject ho_ImageScaled, HTuple hv_Min,
            HTuple hv_Max)
        {




            // Stack for temporary objects 
            HObject[] OTemp = new HObject[20];

            // Local iconic variables 

            HObject ho_SelectedChannel = null, ho_LowerRegion = null;
            HObject ho_UpperRegion = null;

            // Local copy input parameter variables 
            HObject ho_Image_COPY_INP_TMP;
            ho_Image_COPY_INP_TMP = ho_Image.CopyObj(1, -1);



            // Local control variables 

            HTuple hv_LowerLimit = new HTuple(), hv_UpperLimit = new HTuple();
            HTuple hv_Mult = null, hv_Add = null, hv_Channels = null;
            HTuple hv_Index = null, hv_MinGray = new HTuple(), hv_MaxGray = new HTuple();
            HTuple hv_Range = new HTuple();
            HTuple hv_Max_COPY_INP_TMP = hv_Max.Clone();
            HTuple hv_Min_COPY_INP_TMP = hv_Min.Clone();

            // Initialize local and output iconic variables 
            HOperatorSet.GenEmptyObj(out ho_ImageScaled);
            HOperatorSet.GenEmptyObj(out ho_SelectedChannel);
            HOperatorSet.GenEmptyObj(out ho_LowerRegion);
            HOperatorSet.GenEmptyObj(out ho_UpperRegion);
            try
            {
                //Convenience procedure to scale the gray values of the
                //input image Image from the interval [Min,Max]
                //to the interval [0,255] (default).
                //Gray values < 0 or > 255 (after scaling) are clipped.
                //
                //If the image shall be scaled to an interval different from [0,255],
                //this can be achieved by passing tuples with 2 values [From, To]
                //as Min and Max.
                //Example:
                //scale_image_range(Image:ImageScaled:[100,50],[200,250])
                //maps the gray values of Image from the interval [100,200] to [50,250].
                //All other gray values will be clipped.
                //
                //input parameters:
                //Image: the input image
                //Min: the minimum gray value which will be mapped to 0
                //     If a tuple with two values is given, the first value will
                //     be mapped to the second value.
                //Max: The maximum gray value which will be mapped to 255
                //     If a tuple with two values is given, the first value will
                //     be mapped to the second value.
                //
                //output parameter:
                //ImageScale: the resulting scaled image
                //
                if ((int)(new HTuple((new HTuple(hv_Min_COPY_INP_TMP.TupleLength())).TupleEqual(
                    2))) != 0)
                {
                    hv_LowerLimit = hv_Min_COPY_INP_TMP[1];
                    hv_Min_COPY_INP_TMP = hv_Min_COPY_INP_TMP[0];
                }
                else
                {
                    hv_LowerLimit = 0.0;
                }
                if ((int)(new HTuple((new HTuple(hv_Max_COPY_INP_TMP.TupleLength())).TupleEqual(
                    2))) != 0)
                {
                    hv_UpperLimit = hv_Max_COPY_INP_TMP[1];
                    hv_Max_COPY_INP_TMP = hv_Max_COPY_INP_TMP[0];
                }
                else
                {
                    hv_UpperLimit = 255.0;
                }
                //
                //Calculate scaling parameters
                hv_Mult = (((hv_UpperLimit - hv_LowerLimit)).TupleReal()) / (hv_Max_COPY_INP_TMP - hv_Min_COPY_INP_TMP);
                hv_Add = ((-hv_Mult) * hv_Min_COPY_INP_TMP) + hv_LowerLimit;
                //
                //Scale image
                {
                    HObject ExpTmpOutVar_0;
                    HOperatorSet.ScaleImage(ho_Image_COPY_INP_TMP, out ExpTmpOutVar_0, hv_Mult,
                        hv_Add);
                    ho_Image_COPY_INP_TMP.Dispose();
                    ho_Image_COPY_INP_TMP = ExpTmpOutVar_0;
                }
                //
                //Clip gray values if necessary
                //This must be done for each channel separately
                HOperatorSet.CountChannels(ho_Image_COPY_INP_TMP, out hv_Channels);
                HTuple end_val48 = hv_Channels;
                HTuple step_val48 = 1;
                for (hv_Index = 1; hv_Index.Continue(end_val48, step_val48); hv_Index = hv_Index.TupleAdd(step_val48))
                {
                    ho_SelectedChannel.Dispose();
                    HOperatorSet.AccessChannel(ho_Image_COPY_INP_TMP, out ho_SelectedChannel,
                        hv_Index);
                    HOperatorSet.MinMaxGray(ho_SelectedChannel, ho_SelectedChannel, 0, out hv_MinGray,
                        out hv_MaxGray, out hv_Range);
                    ho_LowerRegion.Dispose();
                    HOperatorSet.Threshold(ho_SelectedChannel, out ho_LowerRegion, ((hv_MinGray.TupleConcat(
                        hv_LowerLimit))).TupleMin(), hv_LowerLimit);
                    ho_UpperRegion.Dispose();
                    HOperatorSet.Threshold(ho_SelectedChannel, out ho_UpperRegion, hv_UpperLimit,
                        ((hv_UpperLimit.TupleConcat(hv_MaxGray))).TupleMax());
                    {
                        HObject ExpTmpOutVar_0;
                        HOperatorSet.PaintRegion(ho_LowerRegion, ho_SelectedChannel, out ExpTmpOutVar_0,
                            hv_LowerLimit, "fill");
                        ho_SelectedChannel.Dispose();
                        ho_SelectedChannel = ExpTmpOutVar_0;
                    }
                    {
                        HObject ExpTmpOutVar_0;
                        HOperatorSet.PaintRegion(ho_UpperRegion, ho_SelectedChannel, out ExpTmpOutVar_0,
                            hv_UpperLimit, "fill");
                        ho_SelectedChannel.Dispose();
                        ho_SelectedChannel = ExpTmpOutVar_0;
                    }
                    if ((int)(new HTuple(hv_Index.TupleEqual(1))) != 0)
                    {
                        ho_ImageScaled.Dispose();
                        HOperatorSet.CopyObj(ho_SelectedChannel, out ho_ImageScaled, 1, 1);
                    }
                    else
                    {
                        {
                            HObject ExpTmpOutVar_0;
                            HOperatorSet.AppendChannel(ho_ImageScaled, ho_SelectedChannel, out ExpTmpOutVar_0
                                );
                            ho_ImageScaled.Dispose();
                            ho_ImageScaled = ExpTmpOutVar_0;
                        }
                    }
                }
                ho_Image_COPY_INP_TMP.Dispose();
                ho_SelectedChannel.Dispose();
                ho_LowerRegion.Dispose();
                ho_UpperRegion.Dispose();

                return;
            }
            catch (HalconException HDevExpDefaultException)
            {
                ho_Image_COPY_INP_TMP.Dispose();
                ho_SelectedChannel.Dispose();
                ho_LowerRegion.Dispose();
                ho_UpperRegion.Dispose();

                throw HDevExpDefaultException;
            }
        }


        public Patterm()
        {
            InitializeComponent();
            //窗口缩放用
            x = this.Width;
            y = this.Height;
            setTag(this);
            //窗口缩放用
          
            dialog = new OpenFileDialog();
            m_halconwindow = new hwindows.hwindows();
            // Initialize local and output iconic variables 


            HOperatorSet.GenEmptyObj(out ho_Image);
            
            HOperatorSet.GenEmptyObj(out ho_ImageReduced);
            HOperatorSet.GenEmptyObj(out ho_SelectedContours);
            HOperatorSet.GenEmptyObj(out ho_OEmptyObject1);
            HOperatorSet.GenEmptyObj(out templateRegion);
            HOperatorSet.GenEmptyObj(out ho_resetcross);
            HOperatorSet.GenEmptyObj(out ho_resetrectangle);
            cb_polarity.SelectedIndex = 2;
           // HOperatorSet.GenEmptyObj(out ho_OEmptyObject);
        }

        private void Form1_Resize(object sender, EventArgs e)
        {
            //float newx = (this.Width) / x;
            //float newy = (this.Height) / y;
            //setControls(newx, newy, this);
        }

        private void setTag(Control cons)
        {
            foreach (Control con in cons.Controls)
            {
                con.Tag = con.Width + ";" + con.Height + ";" + con.Left + ";" + con.Top + ";" + con.Font.Size;
                if (con.Controls.Count > 0)
                {
                    setTag(con);
                }
            }
        }
        private void setControls(float newx, float newy, Control cons)
        {
            //遍历窗体中的控件，重新设置控件的值
            foreach (Control con in cons.Controls)
            {
                //获取控件的Tag属性值，并分割后存储字符串数组
                if (con.Tag != null)
                {
                    string[] mytag = con.Tag.ToString().Split(new char[] { ';' });
                    //根据窗体缩放的比例确定控件的值
                    con.Width = Convert.ToInt32(System.Convert.ToSingle(mytag[0]) * newx);//宽度
                    con.Height = Convert.ToInt32(System.Convert.ToSingle(mytag[1]) * newy);//高度
                    con.Left = Convert.ToInt32(System.Convert.ToSingle(mytag[2]) * newx);//左边距
                    con.Top = Convert.ToInt32(System.Convert.ToSingle(mytag[3]) * newy);//顶边距
                    Single currentSize = System.Convert.ToSingle(mytag[4]) * newy;//字体大小
                    con.Font = new Font(con.Font.Name, currentSize, con.Font.Style, con.Font.Unit);
                    if (con.Controls.Count > 0)
                    {
                        setControls(newx, newy, con);
                    }
                }
            }
        }



        private void Patterm_Load(object sender, EventArgs e)
        {
            //加载默认语言
            MultiLanguage multilanguage = new MultiLanguage();
            multilanguage.LoadDefaultLanguage(this, typeof(Patterm));
            string language = Properties.Settings.Default.DefaultLanguage;
            switch (language)
            {
                case "zh":
                    comboBox_selecttype.Items.Clear();
                    comboBox_selecttype.Items.Add("轮型识别");
                    comboBox_selecttype.Items.Add("轮型识别圆心");
                    comboBox_selecttype.Items.Add("气门孔");
                    comboBox_selecttype.Items.Add("找孔区域");
                    break;
                case "en":
                    comboBox_selecttype.Items.Clear();
                    comboBox_selecttype.Items.Add("Wheel type recognition");
                    comboBox_selecttype.Items.Add("Wheel-shaped center");
                    comboBox_selecttype.Items.Add("Valve holes");
                    comboBox_selecttype.Items.Add("Find the hole area");
                    break;
                case "es":
                    comboBox_selecttype.Items.Clear();
                    comboBox_selecttype.Items.Add("Reconocimiento de tipo de rueda");
                    comboBox_selecttype.Items.Add("El tipo de rueda reconoce el centro del círculo");
                    comboBox_selecttype.Items.Add("Orificios de válvula");
                    comboBox_selecttype.Items.Add("Encuentra el área del agujero");
                    break;
                case "fr":
                    comboBox_selecttype.Items.Clear();
                    comboBox_selecttype.Items.Add("moyeux recognize");
                    comboBox_selecttype.Items.Add("moyeux centre");
                    comboBox_selecttype.Items.Add("Trou de soupape");
                    comboBox_selecttype.Items.Add("region hole");
                    break;
            }
                   

          
               
           
            // Initialize local and output iconic variables 
            // LoadImage("demo.png"); ;
            SetDoubleBuffering();
            btn_clearpattrem.Enabled = false;
            btn_add.Enabled = false;
            threshold_textBox.Text = threshold_trackBar.Value.ToString();
            threshold_groupBox.Enabled = false;
            
            WindowParaSet winparset = new WindowParaSet();
            winparset.set_display_font(hWindowControl1.HalconWindow, 20, "mono", "false", "false");
            winparset.set_display_font(hWindowControl2.HalconWindow, 15, "mono", "false", "false");
            HOperatorSet.SetLineWidth(hWindowControl1.HalconWindow, 2);
            HOperatorSet.SetLineWidth(hWindowControl2.HalconWindow, 2);

            //连接数据库
            evenGetDatabasename(ref m_databasename,ref numberofdecimal);
            connection = @"Server=" + m_databasename + ";Database=MyDB;Trusted_Connection=SSPI";
            //ConvertHalcon.Enabled = false;
            //btn_add.Enabled = false;
            //threshold_groupBox.Enabled = false;
        }
        //   private void LoadImage(string ImagePath)
        //   {
        //      OriginImage = new Bitmap(ImagePath);
        //      this.hWindowControl1.Image = OriginImage;
        //  }


        private void SetDoubleBuffering()
        {
            this.SetStyle(ControlStyles.ResizeRedraw, true);
            this.SetStyle(ControlStyles.OptimizedDoubleBuffer, true);
            this.SetStyle(ControlStyles.AllPaintingInWmPaint, true);
            this.SetStyle(ControlStyles.UserPaint, true);
            this.SetStyle(ControlStyles.SupportsTransparentBackColor, true);
            this.UpdateStyles();
        }

     
        /// MouseState=1:当鼠标双击ROI之外的图像区域时候,保留已存在ROI区域;
        /// MouseState=2:根据鼠标在8个矩形框位置来确定是改变宽度还是高度;
        /// </summary>
     

    

        private void ReadImage_button_Click(object sender, EventArgs e)
        {
            



            // Local control variables 

            string file = null;

            dialog.Multiselect = true;//该值确定是否可以选择多个文件
            dialog.Title = res.GetString("selectFile");
            dialog.Filter = res.GetString("allFile"); 

            if (dialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                file = dialog.FileName;
            }
            if (!(file == null))
            {
                try
                {
                    ho_Image.Dispose();

                    m_halconwindow.Read_display(file, hWindowControl1.HalconWindow,out ho_Image, out hv_width, out hv_height);
                   
                    HOperatorSet.DispObj(ho_Image, hWindowControl1.HalconWindow);
                    m_halconwindow.Roi_DrawCircle(hWindowControl1.HalconWindow, out templateRegion);
                   
                       
                }
                catch
                {
                    return;
                }
            }
            ConvertHalcon.Enabled = true;
            btn_clearpattrem.Enabled = true;
            btn_FullWindow.Enabled = true;


        }

        // 剪切图像
        // </summary>
       

        private void ConvertHalcon_Click(object sender, EventArgs e)
        {

            HOperatorSet.DispObj(ho_ImageReduced, hWindowControl2.HalconWindow);
            HObject ho_Border;

            HOperatorSet.GenEmptyObj(out ho_Border);

            if (ho_ImageReduced.CountObj()!=0)
            {
                ho_Border.Dispose();
              //  HOperatorSet.ThresholdSubPix(ho_ImageReduced, out ho_Border, 128);
                m_halconwindow.CreateEdgesByThreshold(ho_ImageReduced, out ho_Border);
                ho_SelectedContours.Dispose();
                ho_SelectedContours.GenEmptyObj();
                switch (comboBox_selecttype.Text)
                {
                    case "轮型识别":
                    case "Wheel type recognition":
                    case "Reconocimiento de tipo de rueda":
                    case "moyeux recognize":
                      //  HOperatorSet.EdgesSubPix(ho_ImageReduced, out ho_SelectedContours, "canny", 1, 20, 40);
                        m_halconwindow.CreageEdges(ho_ImageReduced, out ho_SelectedContours);
                        break;
                    case "轮型识别圆心":
                    case "中心孔":
                    case "帽止口":

                    case "Wheel-shaped center":
                    case "El tipo de rueda reconoce el centro del círculo":
                    case "moyeux centre":

                    case "Center hole":
                    case "Orificio central":
                    case "Trou au centre":

                    case "Cap stop":
                    case "Tope de tapa":
                    case "Bouchon et stop":
                        m_halconwindow.SelectContours(ho_Border,1, out ho_SelectedContours);
                   //     HOperatorSet.SelectContoursXld(ho_Border, out ho_SelectedContours, "open",
                   //50, 200, -0.5, 0.5);
                        HOperatorSet.SetColor(hWindowControl2.HalconWindow, "red");
                        HOperatorSet.DispObj(ho_SelectedContours, hWindowControl2.HalconWindow);
                        break;
                    case "气门孔":
                    case "Valve holes":
                    case "Orificios de válvula":
                    case "Trou de soupape":
                        m_halconwindow.SelectContours(ho_Border, 2, out ho_SelectedContours);
                   //     HOperatorSet.SelectContoursXld(ho_Border, out ho_SelectedContours, "contour_length",
                   //0.5, 2000, -0.5, 0.5);
                        HOperatorSet.SetColor(hWindowControl2.HalconWindow, "red");
                        HOperatorSet.DispObj(ho_SelectedContours, hWindowControl2.HalconWindow);
                        break;


                }


               
                ho_Border.Dispose();
                threshold_groupBox.Enabled = true;


               
            }
            else
            {
                HOperatorSet.ClearWindow(hWindowControl1.HalconWindow);
                //HOperatorSet.SetTposition(hWindowControl1.HalconWindow, 50, 100);
                //HOperatorSet.DispText(hWindowControl1.HalconWindow, res.GetString("loadImg"), "window", "top",
                //    "left", "green", "box", "false");
                m_halconwindow.DispyTest(hWindowControl1.HalconWindow, res.GetString("loadImg"));

               return;
            }

      
        }

        private string getWheelModel()
        {
            try
            {
                SearchPat m_searchPat = new SearchPat();
                var _semaphore = new SemaphoreSlim(Environment.ProcessorCount - 1);
                ConcurrentQueue<SearchPat.ResultData> _keyQueue = new ConcurrentQueue<SearchPat.ResultData>();
                string typename;
                string score;
                string usetime;
                m_searchPat.SearchPattem(ho_Image, hWindowControl1, _semaphore, _keyQueue, out typename, out score, out usetime);
                if (double.Parse(score) > 0.6)
                {
                    return typename;
                }
            }
            catch
            {

            }
            return "";
        }


        private void Save_Click(object sender, EventArgs e)
        {

            Task t_task = Task.Factory.StartNew(() =>
            {
                double[] m_searchregion = { 0.0, 0.0, 0.0, 0.0, 0.0 };
                switch (comboBox_selecttype.Text)
                {
                    #region 轮型识别
                    case "轮型识别":
                    case "Wheel type recognition":
                    case "Reconocimiento de tipo de rueda":
                    case "moyeux recognize":
                        try
                        {
                            string wheelmodel = wheelmodol.Text;
                            if (cbox_zml.Checked)
                            {
                                wheelmodel = getWheelModel();
                                if (wheelmodel == "")
                                {
                                    wheelmodel = Guid.NewGuid().ToString();
                                    HTuple tmp;
                                    m_halconwindow.SaveShape(ho_SelectedContours, Directory.GetCurrentDirectory() + "\\model\\" + wheelmodel + ".shm", 1, out tmp);
                                }
                                var list = new List<string>();
                                if (File.Exists(Directory.GetCurrentDirectory() + "\\model\\" + wheelmodel + ".zml"))
                                {
                                    string hdtxt = File.ReadAllText(Directory.GetCurrentDirectory() + "\\model\\" + wheelmodel + ".zml");
                                    list = hdtxt.Split(new char[] { '\n' }, StringSplitOptions.RemoveEmptyEntries).ToList();
                                }
                                if (!list.Contains(wheelmodol.Text))
                                {
                                    list.Add(wheelmodol.Text);
                                }
                                string text = "";
                                foreach (var line in list)
                                {
                                    text += line.Trim() + "\n";
                                }
                                File.WriteAllText(Directory.GetCurrentDirectory() + "\\model\\" + wheelmodel + ".zml", text);
                            }
                            else
                            {
                                HTuple tmp;
                                m_halconwindow.SaveShape(ho_SelectedContours, Directory.GetCurrentDirectory() + "\\model\\" + wheelmodel + ".shm", 1, out tmp);
                            }
                            break;
                        }

                        catch
                        {
                            MessageBox.Show("请点生成轮廓");
                            break;
                        };
                    #endregion
                    #region 轮型识别圆心
                    case "轮型识别圆心":
                    case "Wheel-shaped center":
                    case "El tipo de rueda reconoce el centro del círculo":
                    case "moyeux centre":
                        try
                        {
                            if ((wheelmodol.Text != "") && (ho_ImageReduced.CountObj() != 0))
                            {
                                string step = "";
                                if (checkBox1.Checked == true)
                                {
                                    HTuple tmp;
                                    m_halconwindow.SaveShape(ho_SelectedContours, Directory.GetCurrentDirectory() + "\\model\\Measurement\\" + wheelmodol.Text + "circenter.shm", 1, out tmp);
                                }


                                if (resetorigin)
                                {
                                    ho_resetcross.Dispose();
                                    HOperatorSet.SetShapeModelOrigin(hv_ModelID3, hv_ModelOriginRow, hv_ModelOriginColumn);
                                }

                                HOperatorSet.GenEmptyObj(out ho_OEmptyObject1);
                                HOperatorSet.GenEmptyObj(out templateRegion);
                                hvec_OEmptyObject.Dispose();

                            }
                            else

                                MessageBox.Show("请输入轮毂型号或选择轮廓!");
                            break;
                        }
                        catch (HalconException ex)
                        {
                            MessageBox.Show("请选择轮廓");
                            break;

                        }
                    #endregion
                    #region 气门孔
                    case "气门孔":
                    case "Valve holes":
                    case "Orificios de válvula":
                    case "Trou de soupape":
                        try
                        {
                            if ((wheelmodol.Text != "") && (ho_ImageReduced.CountObj() != 0))
                            {
                                string step = "";
                                if (checkBox1.Checked == true)
                                {
                                    step = "auto";


                                    HTuple tmp;
                                    m_halconwindow.SaveShape(ho_SelectedContours, Directory.GetCurrentDirectory() + "\\model\\Measurement\\" + wheelmodol.Text + "valvehole .shm", 1, out tmp);
                                }

                                if (resetorigin)
                                {
                                    ho_resetcross.Dispose();
                                    HOperatorSet.SetShapeModelOrigin(hv_ModelID3, hv_ModelOriginRow, hv_ModelOriginColumn);
                                }





                                //  ho_ImageReduced.Dispose();
                                // ho_SelectedContours.Dispose();
                                //  ho_OEmptyObject1.Dispose();//此处不能释放，否则新添加图片再做模板会报错，见btn_add_Click中 ho_OEmptyObject1 显示部分
                                HOperatorSet.GenEmptyObj(out ho_OEmptyObject1);
                                HOperatorSet.GenEmptyObj(out templateRegion);
                                hvec_OEmptyObject.Dispose();
                            }
                            else

                                MessageBox.Show("请输入轮毂型号或选择轮廓!");
                            break;
                        }

                        catch (HalconException ex)
                        {
                            MessageBox.Show("请选择轮廓");
                            break;

                        }
                    #endregion
                    #region 设置气门孔范围
                    case "找孔区域":
                    case "Find the hole area":
                    case "Encuentra el área del agujero":
                    case "region hole":
                        {
                            HTuple hv_radius = new HTuple();
                            bool exist;
                            HOperatorSet.GetDrawingObjectParams(m_halconwindow.hv_DrawID0, "radius", out hv_radius);

                            m_searchregion = hv_radius.ToDArr();
                            // m_searchregion[0] = Math.Floor(m_searchregion[0]);
                            SQlFun m_sqlfun = new SQlFun();
                            m_sqlfun.connection(connection);
                            m_sqlfun.Sql_open();
                            if (m_sqlfun.conn.State == ConnectionState.Open)
                            {
                                //判断是否已经存在
                                exist = m_sqlfun.Sql_ExistColumn("气门孔范围直径", "轮毂型号", wheelmodol.Text);

                                if (exist)
                                {
                                    DialogResult dr = new DialogResult();
                                    dr = MessageBox.Show("此轮毂已经录入，是否替换");

                                    if (dr == DialogResult.OK)
                                    {
                                        m_sqlfun.Sql_modify("气门孔范围直径", "半径", wheelmodol.Text, m_searchregion[0]);
                                    }
                                }
                                else
                                {
                                    int databasemaxindex = m_sqlfun.Sql_indexmax("气门孔范围直径");
                                    databasemaxindex++;
                                    m_sqlfun.Sql_write_searchregion(databasemaxindex, wheelmodol.Text, m_searchregion[0]);
                                }

                                m_sqlfun.conn.Close();

                            }
                            else
                                MessageBox.Show("数据库未打开");

                            break;



                        }
                    #endregion
                    #region 中心孔
                    case "中心孔":
                    case "Center hole":
                    case "Orificio central":
                    case "Trou au centre":
                        try
                        {
                            if ((wheelmodol.Text != "") && (ho_ImageReduced.CountObj() != 0))
                            {
                                string step = "";
                                if (checkBox1.Checked == true)
                                {
                                    // step = "auto";

                                    // HOperatorSet.CreateShapeModelXld(ho_SelectedContours, "auto", (new HTuple(Int32.Parse(nUpDown_StartAngle.Value.ToString()))).TupleRad()
                                    //, (new HTuple(Int32.Parse(nUpDown_AngleRang.Value.ToString()))).TupleRad(), step, "auto", cb_polarity.SelectedItem.ToString(),
                                    //5, out hv_ModelID3);

                                    HTuple tmp;
                                    m_halconwindow.SaveShape(ho_SelectedContours, Directory.GetCurrentDirectory() + "\\model\\Measurement\\" + wheelmodol.Text + "centre.shm", 1, out tmp);
                                }
                                //else

                                //    HOperatorSet.CreateShapeModelXld(ho_SelectedContours, "auto", (new HTuple(Int32.Parse(nUpDown_StartAngle.Value.ToString()))).TupleRad()
                                //        , (new HTuple(Int32.Parse(nUpDown_AngleRang.Value.ToString()))).TupleRad(), (new HTuple(Int32.Parse(nUpDown_AngleStep.Value.ToString()))).TupleRad(), "auto", cb_polarity.SelectedItem.ToString(),
                                //        5, out hv_ModelID3);

                                if (resetorigin)
                                {
                                    ho_resetcross.Dispose();
                                    HOperatorSet.SetShapeModelOrigin(hv_ModelID3, hv_ModelOriginRow, hv_ModelOriginColumn);
                                }
                                //  HOperatorSet.WriteShapeModel(hv_ModelID3, Directory.GetCurrentDirectory() + "\\model\\Measurement\\" + wheelmodol.Text + "centre.shm");
                                //存入注册轮型
                                string str = Directory.GetCurrentDirectory() + "\\model\\registerpicture\\" + wheelmodol.Text + "C";
                                HOperatorSet.WriteImage(ho_Image, "bmp", 0, str);



                                //    ho_Image.Dispose();
                                //   ho_ImageReduced.Dispose();
                                //  ho_SelectedContours.Dispose();
                                //  ho_OEmptyObject1.Dispose();//此处不能释放，否则新添加图片再做模板会报错，见btn_add_Click中 ho_OEmptyObject1 显示部分
                                HOperatorSet.GenEmptyObj(out ho_OEmptyObject1);
                                HOperatorSet.GenEmptyObj(out templateRegion);
                                hvec_OEmptyObject.Dispose();

                            }
                            else

                                MessageBox.Show("请输入轮毂型号或选择轮廓!");
                            break;
                        }
                        catch (HalconException ex)
                        {
                            MessageBox.Show("请选择轮廓");
                            break;

                        }

                    #endregion

                    #region  帽止口
                    case "帽止口":
                    case "Cap stop":
                    case "Tope de tapa":
                    case "Bouchon et stop":
                        try
                        {
                            if ((wheelmodol.Text != "") && (ho_ImageReduced.CountObj() != 0))
                            {
                                string step = "";
                                if (checkBox1.Checked == true)
                                {

                                    HTuple tmp;
                                    m_halconwindow.SaveShape(ho_SelectedContours, Directory.GetCurrentDirectory() + "\\model\\Measurement\\" + wheelmodol.Text + "hat.shm", 1, out tmp);
                                }


                                if (resetorigin)
                                {
                                    ho_resetcross.Dispose();
                                    HOperatorSet.SetShapeModelOrigin(hv_ModelID3, hv_ModelOriginRow, hv_ModelOriginColumn);
                                }


                                //存入注册轮型
                                string str = Directory.GetCurrentDirectory() + "\\model\\registerpicture\\" + wheelmodol.Text + "H";
                                HOperatorSet.WriteImage(ho_Image, "bmp", 0, str);


                                //    ho_Image.Dispose();
                                //   ho_ImageReduced.Dispose();
                                //  ho_SelectedContours.Dispose();
                                //  ho_OEmptyObject1.Dispose();//此处不能释放，否则新添加图片再做模板会报错，见btn_add_Click中 ho_OEmptyObject1 显示部分
                                HOperatorSet.GenEmptyObj(out ho_OEmptyObject1);
                                HOperatorSet.GenEmptyObj(out templateRegion);
                                hvec_OEmptyObject.Dispose();

                            }
                            else
                                MessageBox.Show("请输入轮毂型号或创建轮廓!");
                            break;
                        }
                        catch (HalconException ex)
                        {
                            MessageBox.Show("请创建轮廓");
                            break;

                        }


                        #endregion
                }

                resetorigin = false;
            });
            t_task.Wait();
            MessageBoxEX.Show(res.GetString("savesuccessfully"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
            // MessageBox.Show("保存成功");

        }

        private void cbox_zml_CheckedChanged(object sender, EventArgs e)
        {
        }
        

        private void comboBox_selecttype_TextChanged(object sender, EventArgs e)
        {
            bool visible = comboBox_selecttype.Text.Equals("轮型识别");
            cbox_zml.Visible = false;
        }

        private void wheelmodol_TextChanged(object sender, EventArgs e)
        {
            int len = Convert.ToInt32(typeLength.Text);
            String str = wheelmodol.Text;
            str = str.ToUpper();
            int lent = System.Text.ASCIIEncoding.Default.GetByteCount(str);
            byte[] bb = System.Text.ASCIIEncoding.Default.GetBytes(str);//得到输入的字符串的数组
            if (lent == len)
            {
                wheelmodol.Text = System.Text.ASCIIEncoding.Default.GetString(bb, 0, len);//将截断后的字节数组转换成字符串
            }
            if (lent > len)
            {
                wheelmodol.Text = System.Text.ASCIIEncoding.Default.GetString(bb, 0, len);//将截断后的字节数组转换成字符串
                MessageBox.Show("输入轮型长度超出设置！");
                wheelmodol.SelectionStart = len;
            }
            base.OnTextChanged(e);

        }
        

        private void comboBox_camera_SelectedIndexChanged(object sender, EventArgs e)
        {
            string language = Properties.Settings.Default.DefaultLanguage;
            switch (comboBox_camera.Text)
            {
                case "C1":        
                    switch (language)
                    {
                        case "zh":
                            comboBox_selecttype.Items.Clear();
                            comboBox_selecttype.Items.Add("轮型识别");
                            comboBox_selecttype.Items.Add("轮型识别圆心");
                            comboBox_selecttype.Items.Add("气门孔");
                            comboBox_selecttype.Items.Add("找孔区域");
                            break;
                        case "en":
                            comboBox_selecttype.Items.Clear();
                            comboBox_selecttype.Items.Add("Wheel type recognition");
                            comboBox_selecttype.Items.Add("Wheel-shaped center");
                            comboBox_selecttype.Items.Add("Valve holes");
                            comboBox_selecttype.Items.Add("Find the hole area");
                            break;
                        case "es":
                            comboBox_selecttype.Items.Clear();
                            comboBox_selecttype.Items.Add("Reconocimiento de tipo de rueda");
                            comboBox_selecttype.Items.Add("El tipo de rueda reconoce el centro del círculo");
                            comboBox_selecttype.Items.Add("Orificios de válvula");
                            comboBox_selecttype.Items.Add("Encuentra el área del agujero");
                            break;
                        case "fr":
                            comboBox_selecttype.Items.Clear();
                            comboBox_selecttype.Items.Add("moyeux recognize");
                            comboBox_selecttype.Items.Add("moyeux centre");
                            comboBox_selecttype.Items.Add("Trou de soupape");
                            comboBox_selecttype.Items.Add("region hole");
                            break;
                    }
                    comboBox_selecttype.SelectedIndex = 0;
                    break;

                case "C2":
                    comboBox_selecttype.Items.Clear();
                    switch (language)
                    {
                        case "zh":
                            comboBox_selecttype.Items.Clear();
                            comboBox_selecttype.Text = "中心孔";
                            comboBox_selecttype.Items.Add("中心孔");
                            break;
                        case "en":
                            comboBox_selecttype.Items.Clear();
                            comboBox_selecttype.Text = "Center hole";
                            comboBox_selecttype.Items.Add("Center hole");
                            break;
                        case "es":
                            comboBox_selecttype.Items.Clear();
                            comboBox_selecttype.Text = "Orificio central";
                            comboBox_selecttype.Items.Add("Orificio central");
                            break;
                        case "fr":
                            comboBox_selecttype.Items.Clear();
                            comboBox_selecttype.Text = "Trou au centre";
                            comboBox_selecttype.Items.Add("Trou au centre");
                            break;
                    }
                    break;

                  
                case "C3":
                    comboBox_selecttype.Items.Clear();
                    switch (language)
                    {
                        case "zh":
                            comboBox_selecttype.Items.Clear();
                            comboBox_selecttype.Text = "帽止口";
                            comboBox_selecttype.Items.Add("帽止口");
                            break;
                        case "en":
                            comboBox_selecttype.Items.Clear();
                            comboBox_selecttype.Text = "Cap stop";
                            comboBox_selecttype.Items.Add("Cap stop");
                            break;
                        case "es":
                            comboBox_selecttype.Items.Clear();
                            comboBox_selecttype.Text = "Tope de tapa";
                            comboBox_selecttype.Items.Add("Tope de tapa");
                            break;
                        case "fr":
                            comboBox_selecttype.Items.Clear();
                            comboBox_selecttype.Text = "Bouchon et stop";
                            comboBox_selecttype.Items.Add("Bouchon et stop");
                            break;
                    }
                    break;

            }
        }

        private void hWindowControl_HMouseUp(object sender, HMouseEventArgs e)
        {
           
           if(ho_Image.CountObj() !=0)
            {
                HObject ho_Region;
                HOperatorSet.GenEmptyObj(out ho_Region);
                HOperatorSet.GetDrawingObjectIconic(out ho_Region, m_halconwindow.hv_DrawID0);
                HOperatorSet.ClearWindow(hWindowControl2.HalconWindow);
                ho_ImageReduced.Dispose();
                HOperatorSet.ReduceDomain(ho_Image, ho_Region, out ho_ImageReduced);
               
                m_halconwindow.RegionRectangle(ho_Region, hWindowControl2.HalconWindow, out hv_Rowleft, out hv_Columnleft, out hv_Rowright, out hv_Columnright);
                HOperatorSet.SetPart(hWindowControl2.HalconWindow, hv_Rowleft, hv_Columnleft, hv_Rowright, hv_Columnright);
                HOperatorSet.DispObj(ho_ImageReduced, hWindowControl2.HalconWindow);
            }
           

           
        }

     

        private void btn_add_Click(object sender, EventArgs e)
        {
           

            
        }

        private void btn_clearpattrem_Click(object sender, EventArgs e)
        {

            //ho_ImageReduced.Dispose();
            //ho_SelectedContours.Dispose();

            ho_resetcross.Dispose();
            HOperatorSet.ClearWindow(hWindowControl2.HalconWindow);
            btn_add.Enabled = false;
        }


      
        public void importpatterminfor(string filepath)
        {
            List<fileinfor> InData = new List<fileinfor>();
            for (int i = 14; i < 23; i++)
            {
                DirectoryInfo theFolder = new DirectoryInfo(filepath + i.ToString());

                FileInfo[] dirInfo = theFolder.GetFiles();
                foreach (FileInfo file in dirInfo)
                {
                    fileinfor filf = new fileinfor();
                    filf.filename = file.Name.Substring(0, (file.Name.Length - 4));
                    filf.wheelsize = "inch" + i.ToString();
                    InData.Add(filf);


                }



            }

            for (int k = 0; k < InData.Count; k++)
            {
                ListViewItem _item = new ListViewItem();
                _item.Text = (k + 1).ToString();
                _item.SubItems.Add(InData.ElementAt(k).filename);
                _item.SubItems.Add(InData.ElementAt(k).wheelsize);
                listView_wheeltable.Items.Add(_item);
            }
        }
       



        private void hWindowControl1_HMouseWheel(object sender, HMouseEventArgs e)
        {
            HTuple hv_k = null;
          
            m_halconwindow.ScaleImage(e, hWindowControl2.HalconWindow);
            HOperatorSet.ClearWindow(hWindowControl2.HalconWindow);
            HOperatorSet.DispObj(ho_ImageReduced, hWindowControl2.HalconWindow);
            //缩放后XLD轮廓跟着显示
            HOperatorSet.SetColor(hWindowControl2.HalconWindow, "red");
           
            HOperatorSet.DispObj(ho_SelectedContours, hWindowControl2.HalconWindow);
            HOperatorSet.SetColor(hWindowControl2.HalconWindow, "magenta");
            HOperatorSet.DispObj(ho_OEmptyObject1, hWindowControl2.HalconWindow);


            using (HDevDisposeHelper dh = new HDevDisposeHelper())
            {
                hv_k = new HTuple(hvec_OEmptyObject.Length);
            }

            using (HDevDisposeHelper dh = new HDevDisposeHelper())
            {
                HOperatorSet.SetColor(hWindowControl2.HalconWindow, "spring green");
                for (int i = 0; i < hv_k; i++)

                    HOperatorSet.DispObj(hvec_OEmptyObject[i].O, hWindowControl2.HalconWindow);

            }
        }

        //鼠标按下，记录当前坐标值
        private void hWindowControl1_HMouseDown(object sender, HMouseEventArgs e)
        {
            if (e.Button == System.Windows.Forms.MouseButtons.Right)
            {
                HTuple hv_k = null;
                HTuple Row, Column, Button;
                m_halconwindow.GetMouseDown(e,hWindowControl2.HalconWindow, out Row, out Column);
                RowDown = Row;    //鼠标按下时的行坐标
                ColDown = Column; //鼠标按下时的列坐标

                using (HDevDisposeHelper dh = new HDevDisposeHelper())
                {
                    hv_k = new HTuple(hvec_OEmptyObject.Length);
                }

                using (HDevDisposeHelper dh = new HDevDisposeHelper())
                {
                    HOperatorSet.SetColor(hWindowControl2.HalconWindow, "spring green");
                    for (int i = 0; i < hv_k; i++)

                        HOperatorSet.DispObj(hvec_OEmptyObject[i].O, hWindowControl2.HalconWindow);

                }

            }


        }
        //鼠标抬起，实现图像移动
        private void hWindowControl1_HMouseUp(object sender, HMouseEventArgs e)
        {
            if (e.Button == System.Windows.Forms.MouseButtons.Right)
            {
                HTuple hv_k = null;
               
                m_halconwindow.MoveImage(e, hWindowControl2.HalconWindow, RowDown, ColDown);
                HOperatorSet.ClearWindow(hWindowControl2.HalconWindow);
                HOperatorSet.GetImageSize(ho_ImageReduced, out hv_width, out hv_height);
                if (hv_height != null)
                {
                    HOperatorSet.DispObj(ho_ImageReduced, hWindowControl2.HalconWindow);
                }
                else
                {
                    MessageBoxEX.Show(res.GetString("loadImg1"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
                }
                //移动位置后，XLD轮廓跟着动
                HOperatorSet.SetColor(hWindowControl2.HalconWindow, "red");

                HOperatorSet.DispObj(ho_SelectedContours, hWindowControl2.HalconWindow);
                HOperatorSet.SetColor(hWindowControl2.HalconWindow, "magenta");
                HOperatorSet.DispObj(ho_OEmptyObject1, hWindowControl2.HalconWindow);

                using (HDevDisposeHelper dh = new HDevDisposeHelper())
                {
                    hv_k = new HTuple(hvec_OEmptyObject.Length);
                }

                using (HDevDisposeHelper dh = new HDevDisposeHelper())
                {
                    HOperatorSet.SetColor(hWindowControl2.HalconWindow, "spring green");
                    for (int i = 0; i < hv_k; i++)

                        HOperatorSet.DispObj(hvec_OEmptyObject[i].O, hWindowControl2.HalconWindow);

                }

            }


        }



        //全屏显示图像,使缩放后的图像回到原始大小
        private void btn_FullWindow_Click(object sender, EventArgs e)
        {
            
            HOperatorSet.ClearWindow(hWindowControl2.HalconWindow);
            if (ho_ImageReduced.CountObj() != 0)
            {
                m_halconwindow.ResetImage(hWindowControl2.HalconWindow, hv_Rowleft, hv_Columnleft, hv_Rowright, hv_Columnright);
                
                HOperatorSet.DispObj(ho_ImageReduced, hWindowControl2.HalconWindow);
               
            }
              else
            {
                MessageBoxEX.Show(res.GetString("notArea"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
                return;

            }
               
            if (ho_SelectedContours.CountObj() != 0)
            {
                m_halconwindow.ResetImage(hWindowControl2.HalconWindow, hv_Rowleft, hv_Columnleft, hv_Rowright, hv_Columnright);
              
                HOperatorSet.SetColor(hWindowControl2.HalconWindow, "red");
                HOperatorSet.DispObj(ho_SelectedContours, hWindowControl2.HalconWindow);
            }
            else
                return;


        }

     

        private void threshold_trackBar_Scroll(object sender, EventArgs e)
        {
            try
            {
                threshold_textBox.Text = threshold_trackBar.Value.ToString();

                HObject ho_Border;
                HOperatorSet.ClearWindow(hWindowControl2.HalconWindow);
                HOperatorSet.DispObj(ho_ImageReduced, hWindowControl2.HalconWindow);
                HOperatorSet.GenEmptyObj(out ho_Border);

                if (ho_ImageReduced.CountObj() == 0)
                {
                    m_halconwindow.DispyTest(hWindowControl1.HalconWindow, res.GetString("selectIRO"));
                 
                    return;
                }
                else
                {
                    ho_Border.Dispose();
                    HOperatorSet.ThresholdSubPix(ho_ImageReduced, out ho_Border, this.threshold_trackBar.Value);

                    ho_SelectedContours.Dispose();
                    switch (comboBox_selecttype.Text)
                    {
                        case "轮型识别圆心":
                        case "中心孔":
                        case "帽止口":

                        case "Wheel-shaped center":
                        case "El tipo de rueda reconoce el centro del círculo":
                        case "moyeux centre":

                        case "Center hole":
                        case "Orificio central":
                        case "Trou au centre":

                        case "Cap stop":
                        case "Tope de tapa":
                        case "Bouchon et stop":

                            m_halconwindow.SelectContours(ho_Border,1, out ho_SelectedContours);
                           
                            break;

                        case "气门孔":
                        case "Valve holes":
                        case "Orificios de válvula":
                        case "Trou de soupape":
                            m_halconwindow.SelectContours(ho_Border, 2, out ho_SelectedContours);
                           
                            //  HOperatorSet.DispObj(ho_SelectedContours, hWindowControl2.HalconID);
                            break;
                    }

                    HOperatorSet.SetColor(hWindowControl2.HalconWindow, "red");

                    HOperatorSet.DispObj(ho_SelectedContours, hWindowControl2.HalconWindow);

                    ho_Border.Dispose();

                }
            }
            catch { }

        }

        private void TrainPattemClosed(object sender, FormClosedEventArgs e)
        {
            try
            {
                
                ho_ImageReduced.Dispose();
                ho_SelectedContours.Dispose();
                ho_OEmptyObject1.Dispose();
                templateRegion.Dispose();
                ho_Image.Dispose();


            }
            catch
            {
                return;
            }
           
        }
        
  


    }
}
