<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <configSections>
        <sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
            <section name="BingSolhalcon.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false"/>
        </sectionGroup>
        <section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler,log4net"/>
        <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
        <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false"/>
    </configSections>
    <startup>       
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8"/>
    </startup>
    <userSettings>
        <BingSolhalcon.Properties.Settings>
            <setting name="DefaultLanguage" serializeAs="String">
                <value>zh</value>
            </setting>
        </BingSolhalcon.Properties.Settings>
    </userSettings>
    <log4net>
        <appender name="file" type="log4net.Appender.RollingFileAppender">
            <file value="log.txt"/>
            <appendToFile value="true"/>
            <rollingStyle value="Size"/>
            <maxSizeRollBackups value="10"/>
            <maximumFileSize value="5MB"/>
            <staticLogFileName value="true"/>
            <layout type="log4net.Layout.PatternLayout">
                <param name="ConversionPattern" value="%d [%t] %-5p - %m%n"/>
            </layout>
        </appender>
        <logger name="logger">
            <level value="ALL"/>
            <appender-ref ref="file"/>
        </logger>
    </log4net>
</configuration>
